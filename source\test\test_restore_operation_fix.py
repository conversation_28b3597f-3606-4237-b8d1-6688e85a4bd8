#!/usr/bin/env python3
"""
Test script to verify the restore operation fix.
Tests the corrected restore functionality with proper database path handling.
"""

import os
import sys
import tempfile
import shutil
import sqlite3
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backup.backup_manager import BackupManager
from backup.restore_manager import RestoreManager
from DB.db_connection import <PERSON><PERSON>ool<PERSON>ana<PERSON>


def test_restore_operation_fix():
    """Test the fixed restore operation."""
    print('[TEST] Starting restore operation fix test')
    
    # Create temporary directory for test
    test_dir = tempfile.mkdtemp(prefix='clipmore_restore_fix_test_')
    print(f'[TEST] Using test directory: {test_dir}')
    
    try:
        # Step 1: Create a backup
        print('\n[STEP 1] Creating test backup...')
        backup_manager = BackupManager()
        
        backup_filename = f"restore_test_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join(test_dir, backup_filename)
        
        backup_success = backup_manager.create_backup(backup_path, {
            'compression': False,
            'verification': True
        })
        
        if not backup_success:
            print('[ERROR] Backup creation failed')
            return False
        
        print(f'[STEP 1] ✅ Backup created: {backup_path}')
        
        # Step 2: Test RestoreManager initialization
        print('\n[STEP 2] Testing RestoreManager initialization...')
        restore_manager = RestoreManager()
        
        # Check that the connection pool has the correct attributes
        if not hasattr(restore_manager.connection_pool, 'db_path'):
            print('[ERROR] ConnectionPoolManager missing db_path attribute')
            return False
        
        if not hasattr(restore_manager.connection_pool, 'close_all'):
            print('[ERROR] ConnectionPoolManager missing close_all method')
            return False
        
        print(f'[STEP 2] ✅ RestoreManager initialized with db_path: {restore_manager.connection_pool.db_path}')
        
        # Step 3: Test backup file validation
        print('\n[STEP 3] Testing backup validation...')
        
        if not restore_manager._validate_backup_file(backup_path):
            print('[ERROR] Backup file validation failed')
            return False
        
        print('[STEP 3] ✅ Backup file validation passed')
        
        # Step 4: Test backup integrity verification
        print('\n[STEP 4] Testing backup integrity verification...')
        
        if not restore_manager._verify_backup_integrity(backup_path):
            print('[ERROR] Backup integrity verification failed')
            return False
        
        print('[STEP 4] ✅ Backup integrity verification passed')
        
        # Step 5: Test database structure verification
        print('\n[STEP 5] Testing database structure verification...')
        
        if not restore_manager._verify_backup_database_structure(backup_path):
            print('[ERROR] Database structure verification failed')
            return False
        
        print('[STEP 5] ✅ Database structure verification passed')
        
        # Step 6: Test the _perform_database_restore method (without actually restoring)
        print('\n[STEP 6] Testing database restore preparation...')
        
        try:
            # Get the current database path using the fixed attribute
            current_db_path = restore_manager.connection_pool.db_path
            print(f'[STEP 6] Current database path: {current_db_path}')
            
            if not current_db_path:
                print('[ERROR] Database path is empty')
                return False
            
            if not os.path.exists(current_db_path):
                print('[ERROR] Current database file does not exist')
                return False
            
            print('[STEP 6] ✅ Database restore preparation passed')
            
        except Exception as e:
            print(f'[ERROR] Database restore preparation failed: {e}')
            return False
        
        # Step 7: Test connection pool close_all method
        print('\n[STEP 7] Testing connection pool close_all method...')
        
        try:
            # Test that we can call close_all without errors
            restore_manager.connection_pool.close_all()
            print('[STEP 7] ✅ Connection pool close_all method works')
            
        except Exception as e:
            print(f'[ERROR] Connection pool close_all failed: {e}')
            return False
        
        print('\n[TEST] ✅ ALL RESTORE OPERATION FIX TESTS PASSED!')
        
        # Print fix summary
        print('\n🔧 FIX SUMMARY:')
        print('  • Fixed database_path → db_path attribute access')
        print('  • Fixed close_all_connections → close_all method call')
        print('  • Verified ConnectionPoolManager attribute compatibility')
        print('  • Confirmed restore operation preparation works')
        
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test directory
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f'\n[CLEANUP] Test directory cleaned up: {test_dir}')


def test_actual_restore_operation():
    """Test an actual restore operation with a temporary database."""
    print('\n[ADVANCED TEST] Testing actual restore operation...')
    
    # Create temporary directory for test
    test_dir = tempfile.mkdtemp(prefix='clipmore_actual_restore_test_')
    print(f'[ADVANCED TEST] Using test directory: {test_dir}')
    
    try:
        # Create a temporary database for testing
        temp_db_path = os.path.join(test_dir, 'temp_test.db')
        
        # Copy the current database to temp location
        current_db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'DB', 'clipsmore_db.db')
        if os.path.exists(current_db_path):
            shutil.copy2(current_db_path, temp_db_path)
            print(f'[ADVANCED TEST] Created temporary database: {temp_db_path}')
        else:
            print('[ADVANCED TEST] Skipping actual restore test - main database not found')
            return True
        
        # Create a backup of the temp database
        backup_path = os.path.join(test_dir, 'test_backup.db')
        backup_manager = BackupManager()
        
        # Temporarily use the temp database
        original_pool = backup_manager.connection_pool
        backup_manager.connection_pool = ConnectionPoolManager(temp_db_path)
        
        backup_success = backup_manager.create_backup(backup_path, {
            'compression': False,
            'verification': True
        })
        
        if not backup_success:
            print('[ADVANCED TEST] Backup creation failed')
            return False
        
        print(f'[ADVANCED TEST] Backup created: {backup_path}')
        
        # Now test restore
        restore_manager = RestoreManager()
        restore_manager.connection_pool = ConnectionPoolManager(temp_db_path)
        
        # Test the restore operation
        restore_success = restore_manager.restore_from_backup(backup_path, {
            'verification': True,
            'backup_current': True,
            'restore_type': 'full'
        })
        
        if restore_success:
            print('[ADVANCED TEST] ✅ Actual restore operation succeeded!')
        else:
            print('[ADVANCED TEST] ❌ Actual restore operation failed')
            return False
        
        return True
        
    except Exception as e:
        print(f'[ADVANCED TEST] Failed with exception: {e}')
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Clean up test directory
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f'[ADVANCED TEST] Test directory cleaned up: {test_dir}')


if __name__ == '__main__':
    print('=' * 70)
    print('RESTORE OPERATION FIX TEST')
    print('Testing the corrected restore functionality')
    print('=' * 70)
    
    # Run basic fix test
    basic_test_passed = test_restore_operation_fix()
    
    # Run advanced actual restore test
    advanced_test_passed = test_actual_restore_operation()
    
    print('=' * 70)
    print('TEST RESULTS:')
    print(f'  Basic Fix Test: {"PASSED" if basic_test_passed else "FAILED"}')
    print(f'  Advanced Restore Test: {"PASSED" if advanced_test_passed else "FAILED"}')
    
    if basic_test_passed and advanced_test_passed:
        print('  OVERALL: ALL TESTS PASSED ✅')
        print('\n🎉 RESTORE OPERATION FIX SUCCESSFUL!')
        print('The restore functionality should now work correctly.')
        sys.exit(0)
    else:
        print('  OVERALL: SOME TESTS FAILED ❌')
        print('\n❌ RESTORE OPERATION FIX NEEDS MORE WORK!')
        sys.exit(1)
