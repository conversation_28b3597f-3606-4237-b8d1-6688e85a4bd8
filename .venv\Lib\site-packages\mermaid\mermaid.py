import uuid
import requests
import base64

class Mermaid:
    def __init__(self, diagram: str):
        self._diagram = self._process_diagram(diagram)
        self._uid = uuid.uuid4()

    @staticmethod
    def _process_diagram(diagram: str) -> str:
        graphbytes = diagram.encode("utf8")
        base64_bytes = base64.b64encode(graphbytes)
        diagram = base64_bytes.decode("ascii")
        return diagram

    def _repr_html_(self) -> str:
        
        response = requests.get("https://mermaid.ink/svg/" + self._diagram)
        return response.text