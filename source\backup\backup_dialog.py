#!/usr/bin/env python3
"""
Backup Dialog for ClipsMore
Provides user interface for backup and restore operations.
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backup.backup_manager import BackupManager
from utils.directory_manager import DirectoryManager


class BackupDialog(tk.Toplevel):
    """
    Backup dialog for ClipsMore application.
    Provides interface for backup creation, history, and restore operations.
    """
    
    def __init__(self, parent, database_manager=None, theme_manager=None):
        """Initialize the backup dialog."""
        print('[DEBUG] BackupDialog.__init__ called')
        super().__init__(parent)
        
        self.parent = parent
        self.database_manager = database_manager
        self.theme_manager = theme_manager
        self.backup_manager = BackupManager(database_manager)
        self.directory_manager = DirectoryManager()

        # Dialog state
        self.backup_in_progress = False
        self.backup_path = tk.StringVar()
        self.compression_enabled = tk.BooleanVar(value=True)
        self.verification_enabled = tk.BooleanVar(value=True)
        
        # Setup dialog
        self._setup_dialog()
        self._create_interface()
        self._apply_theme()
        self._load_backup_history()
        
        # Set progress callback
        self.backup_manager.set_progress_callback(self._update_progress)
    
    def _setup_dialog(self):
        """Setup dialog properties."""
        print('[DEBUG] BackupDialog._setup_dialog called')
        
        self.title("Backup & Restore - ClipsMore")
        self.geometry("700x600")
        self.resizable(True, True)
        
        # Center on parent
        self.transient(self.parent)
        self.grab_set()
        
        # Position relative to parent
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        x = parent_x + (parent_width // 2) - 350
        y = parent_y + (parent_height // 2) - 300
        
        self.geometry(f"700x600+{x}+{y}")
    
    def _create_interface(self):
        """Create the dialog interface."""
        print('[DEBUG] BackupDialog._create_interface called')
        
        # Main container
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create backup tab
        backup_frame = ttk.Frame(notebook)
        notebook.add(backup_frame, text="Create Backup")
        self._create_backup_tab(backup_frame)
        
        # Backup history tab
        history_frame = ttk.Frame(notebook)
        notebook.add(history_frame, text="Backup History")
        self._create_history_tab(history_frame)
        
        # Restore tab
        restore_frame = ttk.Frame(notebook)
        notebook.add(restore_frame, text="Restore")
        self._create_restore_tab(restore_frame)
        
        # Progress and buttons
        self._create_progress_section(main_frame)
        self._create_button_section(main_frame)
    
    def _create_backup_tab(self, parent):
        """Create backup creation tab."""
        print('[DEBUG] BackupDialog._create_backup_tab called')
        
        # Backup location
        location_group = ttk.LabelFrame(parent, text="Backup Location")
        location_group.pack(fill=tk.X, padx=5, pady=5)
        
        path_frame = ttk.Frame(location_group)
        path_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Entry(
            path_frame,
            textvariable=self.backup_path,
            width=50
        ).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(
            path_frame,
            text="Browse...",
            command=self._browse_backup_path
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Quick location buttons
        quick_frame = ttk.Frame(location_group)
        quick_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(
            quick_frame,
            text="Desktop",
            command=lambda: self._set_quick_location("Desktop")
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            quick_frame,
            text="Documents",
            command=lambda: self._set_quick_location("Documents")
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            quick_frame,
            text="Default",
            command=self._set_default_backup_location
        ).pack(side=tk.LEFT)
        
        # Backup options
        options_group = ttk.LabelFrame(parent, text="Backup Options")
        options_group.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Checkbutton(
            options_group,
            text="Enable compression (reduces file size)",
            variable=self.compression_enabled
        ).pack(anchor=tk.W, padx=10, pady=2)
        
        ttk.Checkbutton(
            options_group,
            text="Verify backup integrity after creation",
            variable=self.verification_enabled
        ).pack(anchor=tk.W, padx=10, pady=2)
        
        # Backup info
        info_group = ttk.LabelFrame(parent, text="Database Information")
        info_group.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.info_text = tk.Text(info_group, height=8, wrap=tk.WORD, state=tk.DISABLED)
        info_scrollbar = ttk.Scrollbar(info_group, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=5)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Load database info
        self._load_database_info()
    
    def _create_history_tab(self, parent):
        """Create backup history tab."""
        print('[DEBUG] BackupDialog._create_history_tab called')
        
        # History controls
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(
            control_frame,
            text="Refresh",
            command=self._load_backup_history
        ).pack(side=tk.LEFT)
        
        ttk.Button(
            control_frame,
            text="Delete Selected",
            command=self._delete_selected_backup
        ).pack(side=tk.LEFT, padx=(5, 0))
        
        # History list
        history_frame = ttk.Frame(parent)
        history_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview for backup history
        columns = ('Date', 'Type', 'Size', 'Status', 'Path')
        self.history_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.history_tree.heading('Date', text='Date')
        self.history_tree.heading('Type', text='Type')
        self.history_tree.heading('Size', text='Size')
        self.history_tree.heading('Status', text='Status')
        self.history_tree.heading('Path', text='Path')
        
        self.history_tree.column('Date', width=150)
        self.history_tree.column('Type', width=80)
        self.history_tree.column('Size', width=100)
        self.history_tree.column('Status', width=80)
        self.history_tree.column('Path', width=250)
        
        # Scrollbars
        history_v_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        history_h_scrollbar = ttk.Scrollbar(history_frame, orient=tk.HORIZONTAL, command=self.history_tree.xview)
        self.history_tree.configure(yscrollcommand=history_v_scrollbar.set, xscrollcommand=history_h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        history_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind click event to populate restore path
        self.history_tree.bind('<Button-1>', self._on_history_click)
        self.history_tree.bind('<Double-Button-1>', self._on_history_double_click)
    
    def _create_restore_tab(self, parent):
        """Create restore tab."""
        print('[DEBUG] BackupDialog._create_restore_tab called')
        
        # Restore source
        source_group = ttk.LabelFrame(parent, text="Restore Source")
        source_group.pack(fill=tk.X, padx=5, pady=5)
        
        self.restore_path = tk.StringVar()
        
        path_frame = ttk.Frame(source_group)
        path_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Entry(
            path_frame,
            textvariable=self.restore_path,
            width=50
        ).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(
            path_frame,
            text="Browse...",
            command=self._browse_restore_file
        ).pack(side=tk.RIGHT, padx=(5, 0))

        # Quick location buttons for restore
        quick_restore_frame = ttk.Frame(source_group)
        quick_restore_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(
            quick_restore_frame,
            text="Desktop",
            command=lambda: self._set_quick_restore_location("Desktop")
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            quick_restore_frame,
            text="Documents",
            command=lambda: self._set_quick_restore_location("Documents")
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            quick_restore_frame,
            text="Default",
            command=self._browse_default_restore_location
        ).pack(side=tk.LEFT)
        
        # Restore options
        restore_options_group = ttk.LabelFrame(parent, text="Restore Options")
        restore_options_group.pack(fill=tk.X, padx=5, pady=5)
        
        self.create_backup_before_restore = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(
            restore_options_group,
            text="Create backup before restore (recommended)",
            variable=self.create_backup_before_restore
        ).pack(anchor=tk.W, padx=10, pady=2)
        
        # Warning
        warning_frame = ttk.Frame(restore_options_group)
        warning_frame.pack(fill=tk.X, padx=10, pady=5)
        
        warning_label = ttk.Label(
            warning_frame,
            text="⚠️ Warning: Restore will replace all current data!",
            foreground="red"
        )
        warning_label.pack(anchor=tk.W)
        
        # Restore info
        restore_info_group = ttk.LabelFrame(parent, text="Backup Information")
        restore_info_group.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.restore_info_text = tk.Text(restore_info_group, height=8, wrap=tk.WORD, state=tk.DISABLED)
        restore_info_scrollbar = ttk.Scrollbar(restore_info_group, orient=tk.VERTICAL, command=self.restore_info_text.yview)
        self.restore_info_text.configure(yscrollcommand=restore_info_scrollbar.set)
        
        self.restore_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=5)
        restore_info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Restore button
        ttk.Button(
            parent,
            text="Start Restore",
            command=self._start_restore
        ).pack(pady=10)
    
    def _create_progress_section(self, parent):
        """Create progress section."""
        print('[DEBUG] BackupDialog._create_progress_section called')
        
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        self.progress_label = ttk.Label(progress_frame, text="Ready")
        self.progress_label.pack(anchor=tk.W)
    
    def _create_button_section(self, parent):
        """Create button section."""
        print('[DEBUG] BackupDialog._create_button_section called')

        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(
            button_frame,
            text="Close",
            command=self._close_dialog
        ).pack(side=tk.RIGHT, padx=(5, 0))

        self.restore_button = ttk.Button(
            button_frame,
            text="Start Restore",
            command=self._start_restore
        )
        self.restore_button.pack(side=tk.RIGHT, padx=(5, 0))

        self.backup_button = ttk.Button(
            button_frame,
            text="Create Backup",
            command=self._start_backup
        )
        self.backup_button.pack(side=tk.RIGHT)
    
    def _apply_theme(self):
        """Apply theme to dialog."""
        if self.theme_manager:
            # Apply theme colors and styles
            pass
    
    def _browse_backup_path(self):
        """Browse for backup file path."""
        print('[DEBUG] BackupDialog._browse_backup_path called')

        # Determine initial directory based on current backup path or default location
        initial_dir = None
        initial_file = None

        current_path = self.backup_path.get()
        if current_path:
            # If there's already a path set, use its directory
            initial_dir = os.path.dirname(current_path)
            initial_file = os.path.basename(current_path)
            print(f'[DEBUG] Using current path directory: {initial_dir}')
        else:
            # No path set, determine best default location
            try:
                # Try Documents first (most common user preference)
                documents_path = self.directory_manager.get_documents_path()
                if self.directory_manager.validate_directory(str(documents_path)):
                    initial_dir = str(documents_path)
                    print(f'[DEBUG] Using Documents directory: {initial_dir}')
                else:
                    # Fall back to Desktop
                    desktop_path = self.directory_manager.get_desktop_path()
                    if self.directory_manager.validate_directory(str(desktop_path)):
                        initial_dir = str(desktop_path)
                        print(f'[DEBUG] Using Desktop directory: {initial_dir}')
                    else:
                        # Last resort - use current working directory
                        initial_dir = os.getcwd()
                        print(f'[DEBUG] Using current working directory: {initial_dir}')
            except Exception as e:
                print(f'[WARNING] Error determining initial directory: {e}')
                initial_dir = os.getcwd()

        # Generate default filename if not already set
        if not initial_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            initial_file = f"clipsmore_backup_{timestamp}.db"

        filename = filedialog.asksaveasfilename(
            parent=self,
            title="Save Backup As",
            initialdir=initial_dir,
            initialfile=initial_file,
            filetypes=[("Database files", "*.db"), ("Compressed files", "*.gz"), ("All files", "*.*")],
            defaultextension=".db"
        )

        if filename:
            self.backup_path.set(filename)
            print(f'[DEBUG] Backup path set to: {filename}')
    
    def _set_quick_location(self, location: str):
        """Set backup path to a quick location using enhanced directory detection."""
        print(f'[DEBUG] BackupDialog._set_quick_location called for {location}')

        try:
            if location == "Desktop":
                path = self.directory_manager.get_desktop_path()
            elif location == "Documents":
                path = self.directory_manager.get_documents_path()
            else:
                print(f'[WARNING] Unknown location: {location}')
                return

            # Validate the directory exists and is writable
            if not self.directory_manager.validate_directory(str(path)):
                print(f'[WARNING] Directory not accessible: {path}')
                messagebox.showwarning(
                    "Directory Access",
                    f"Cannot access {location} directory: {path}\nUsing default location instead."
                )
                self._set_default_backup_location()
                return

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"clipsmore_backup_{timestamp}.db"
            full_path = path / filename

            self.backup_path.set(str(full_path))
            print(f'[DEBUG] Set backup path to: {full_path}')

        except Exception as e:
            print(f'[ERROR] Error setting quick location {location}: {e}')
            messagebox.showerror("Error", f"Error accessing {location} directory: {e}")
            self._set_default_backup_location()
    
    def _set_default_backup_location(self):
        """Set default backup location."""
        print('[DEBUG] BackupDialog._set_default_backup_location called')
        
        # Create backups directory in application folder
        app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        backup_dir = os.path.join(app_dir, "backups")
        
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"clipsmore_backup_{timestamp}.db"
        full_path = os.path.join(backup_dir, filename)
        
        self.backup_path.set(full_path)
    
    def _load_database_info(self):
        """Load and display database information."""
        print('[DEBUG] BackupDialog._load_database_info called')
        
        try:
            # Get database statistics
            info_text = "Database Information:\n\n"
            
            # Add basic info
            info_text += f"Database Location: {self.backup_manager.connection_pool.db_path}\n"
            info_text += f"Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            
            # Add table counts (this would need to be implemented)
            info_text += "Table Statistics:\n"
            info_text += "- Clips: Loading...\n"
            info_text += "- Business Cases: Loading...\n"
            info_text += "- Components: Loading...\n"
            info_text += "- Assignments: Loading...\n"
            
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            self.info_text.insert(1.0, info_text)
            self.info_text.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f'[ERROR] Failed to load database info: {e}')
    
    def _load_backup_history(self):
        """Load backup history into the treeview."""
        print('[DEBUG] BackupDialog._load_backup_history called')
        
        try:
            # Clear existing items
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)
            
            # Get backup history
            history = self.backup_manager.get_backup_history()
            
            for backup in history:
                # Format data for display
                date_str = backup.get('created_date', '')
                if date_str:
                    try:
                        date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
                        date_str = date_obj.strftime('%Y-%m-%d %H:%M')
                    except:
                        pass
                
                size_str = self._format_file_size(backup.get('file_size', 0))
                
                self.history_tree.insert('', tk.END, values=(
                    date_str,
                    backup.get('backup_type', 'full'),
                    size_str,
                    backup.get('verification_status', 'unknown'),
                    backup.get('backup_path', '')
                ))
                
        except Exception as e:
            print(f'[ERROR] Failed to load backup history: {e}')
            messagebox.showerror("Error", f"Failed to load backup history: {e}")
    
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def _delete_selected_backup(self):
        """Delete selected backup from history."""
        print('[DEBUG] BackupDialog._delete_selected_backup called')

        selected = self.history_tree.selection()
        if not selected:
            messagebox.showwarning("No Selection", "Please select a backup to delete.")
            return

        # Confirm deletion
        if messagebox.askyesno("Confirm Delete", "Are you sure you want to delete the selected backup?"):
            # Implementation would go here
            messagebox.showinfo("Not Implemented", "Backup deletion is not yet implemented.")

    def _on_history_click(self, event):
        """Handle single click on backup history item."""
        print('[DEBUG] BackupDialog._on_history_click called')

        # Get the clicked item
        item = self.history_tree.identify('item', event.x, event.y)
        if item:
            # Get the backup path from the clicked item
            values = self.history_tree.item(item, 'values')
            if values and len(values) >= 5:  # Ensure we have all columns
                backup_path = values[4]  # Path is the 5th column (index 4)
                if backup_path and os.path.exists(backup_path):
                    # Populate the restore path field
                    self.restore_path.set(backup_path)
                    print(f'[DEBUG] Set restore path from history: {backup_path}')

                    # Switch to restore tab and analyze the file
                    notebook = self.nametowidget(self.winfo_children()[0].winfo_children()[0])  # Get notebook widget
                    notebook.select(2)  # Select restore tab (index 2)
                    self._analyze_restore_file(backup_path)

                    # Show feedback to user
                    self.progress_label.config(text=f"Selected backup: {os.path.basename(backup_path)}")
                else:
                    messagebox.showwarning("File Not Found", f"Backup file not found:\n{backup_path}")

    def _on_history_double_click(self, event):
        """Handle double click on backup history item."""
        print('[DEBUG] BackupDialog._on_history_double_click called')

        # Get the clicked item
        item = self.history_tree.identify('item', event.x, event.y)
        if item:
            # Get the backup path from the clicked item
            values = self.history_tree.item(item, 'values')
            if values and len(values) >= 5:  # Ensure we have all columns
                backup_path = values[4]  # Path is the 5th column (index 4)
                if backup_path and os.path.exists(backup_path):
                    # Show backup details
                    self._show_backup_details(backup_path, values)
                else:
                    messagebox.showwarning("File Not Found", f"Backup file not found:\n{backup_path}")

    def _show_backup_details(self, backup_path, values):
        """Show detailed information about a backup."""
        print(f'[DEBUG] BackupDialog._show_backup_details called for {backup_path}')

        # Create details dialog
        details_dialog = tk.Toplevel(self)
        details_dialog.title("Backup Details")
        details_dialog.geometry("500x400")
        details_dialog.transient(self)
        details_dialog.grab_set()

        # Center the dialog
        details_dialog.geometry("+%d+%d" % (
            self.winfo_rootx() + 50,
            self.winfo_rooty() + 50
        ))

        # Create details content
        main_frame = ttk.Frame(details_dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Backup information
        info_frame = ttk.LabelFrame(main_frame, text="Backup Information")
        info_frame.pack(fill=tk.X, pady=(0, 10))

        details = [
            ("Date:", values[0]),
            ("Type:", values[1]),
            ("Size:", values[2]),
            ("Status:", values[3]),
            ("Path:", values[4])
        ]

        for i, (label, value) in enumerate(details):
            ttk.Label(info_frame, text=label, font=('TkDefaultFont', 9, 'bold')).grid(
                row=i, column=0, sticky=tk.W, padx=10, pady=2
            )
            ttk.Label(info_frame, text=value).grid(
                row=i, column=1, sticky=tk.W, padx=10, pady=2
            )

        # File information
        if os.path.exists(backup_path):
            file_info_frame = ttk.LabelFrame(main_frame, text="File Information")
            file_info_frame.pack(fill=tk.X, pady=(0, 10))

            file_stat = os.stat(backup_path)
            file_details = [
                ("File Size:", self._format_file_size(file_stat.st_size)),
                ("Modified:", datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')),
                ("Accessible:", "Yes" if os.access(backup_path, os.R_OK) else "No")
            ]

            for i, (label, value) in enumerate(file_details):
                ttk.Label(file_info_frame, text=label, font=('TkDefaultFont', 9, 'bold')).grid(
                    row=i, column=0, sticky=tk.W, padx=10, pady=2
                )
                ttk.Label(file_info_frame, text=value).grid(
                    row=i, column=1, sticky=tk.W, padx=10, pady=2
                )

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(
            button_frame,
            text="Use for Restore",
            command=lambda: self._use_backup_for_restore(backup_path, details_dialog)
        ).pack(side=tk.LEFT)

        ttk.Button(
            button_frame,
            text="Close",
            command=details_dialog.destroy
        ).pack(side=tk.RIGHT)

    def _use_backup_for_restore(self, backup_path, dialog):
        """Use selected backup for restore and close details dialog."""
        print(f'[DEBUG] BackupDialog._use_backup_for_restore called for {backup_path}')

        self.restore_path.set(backup_path)
        notebook = self.nametowidget(self.winfo_children()[0].winfo_children()[0])  # Get notebook widget
        notebook.select(2)  # Select restore tab (index 2)
        self._analyze_restore_file(backup_path)

        dialog.destroy()
        self.progress_label.config(text=f"Ready to restore: {os.path.basename(backup_path)}")
    
    def _browse_restore_file(self):
        """Browse for restore file."""
        print('[DEBUG] BackupDialog._browse_restore_file called')
        
        filename = filedialog.askopenfilename(
            parent=self,
            title="Select Backup File to Restore",
            filetypes=[("Database files", "*.db"), ("Compressed files", "*.gz"), ("All files", "*.*")]
        )
        
        if filename:
            self.restore_path.set(filename)
            self._analyze_restore_file(filename)
    
    def _set_quick_restore_location(self, location: str):
        """Set restore path to a quick location using enhanced directory detection."""
        print(f'[DEBUG] BackupDialog._set_quick_restore_location called for {location}')

        try:
            if location == "Desktop":
                path = self.directory_manager.get_desktop_path()
            elif location == "Documents":
                path = self.directory_manager.get_documents_path()
            else:
                print(f'[WARNING] Unknown restore location: {location}')
                return

            # Look for backup files in the directory
            backup_files = []
            for file_path in path.glob("*.db"):
                if "backup" in file_path.name.lower():
                    backup_files.append(file_path)

            for file_path in path.glob("*.gz"):
                if "backup" in file_path.name.lower():
                    backup_files.append(file_path)

            if backup_files:
                # Sort by modification time, newest first
                backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                latest_backup = backup_files[0]
                self.restore_path.set(str(latest_backup))
                self._analyze_restore_file(str(latest_backup))
                print(f'[DEBUG] Set restore path to latest backup: {latest_backup}')
            else:
                # No backup files found, just set the directory for browsing
                messagebox.showinfo(
                    "No Backups Found",
                    f"No backup files found in {location}.\nPlease browse for a backup file."
                )

        except Exception as e:
            print(f'[ERROR] Error setting quick restore location {location}: {e}')
            messagebox.showerror("Error", f"Error accessing {location} directory: {e}")

    def _browse_default_restore_location(self):
        """Browse for backup files in the default backup location."""
        print('[DEBUG] BackupDialog._browse_default_restore_location called')

        try:
            # Get default backup directory
            app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            backup_dir = os.path.join(app_dir, "backups")

            if os.path.exists(backup_dir):
                # Look for backup files in default directory
                backup_files = []
                for file_name in os.listdir(backup_dir):
                    if file_name.endswith(('.db', '.gz')) and 'backup' in file_name.lower():
                        backup_files.append(os.path.join(backup_dir, file_name))

                if backup_files:
                    # Sort by modification time, newest first
                    backup_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                    latest_backup = backup_files[0]
                    self.restore_path.set(latest_backup)
                    self._analyze_restore_file(latest_backup)
                    print(f'[DEBUG] Set restore path to latest default backup: {latest_backup}')
                else:
                    messagebox.showinfo(
                        "No Backups Found",
                        f"No backup files found in default location:\n{backup_dir}"
                    )
            else:
                messagebox.showinfo(
                    "Default Location Not Found",
                    f"Default backup directory does not exist:\n{backup_dir}"
                )

        except Exception as e:
            print(f'[ERROR] Error browsing default restore location: {e}')
            messagebox.showerror("Error", f"Error accessing default backup location: {e}")

    def _analyze_restore_file(self, file_path: str):
        """Analyze restore file and display information."""
        print(f'[DEBUG] BackupDialog._analyze_restore_file called for {file_path}')

        try:
            info_text = f"Backup File Analysis:\n\n"
            info_text += f"File: {file_path}\n"
            info_text += f"Size: {self._format_file_size(os.path.getsize(file_path))}\n"
            info_text += f"Modified: {datetime.fromtimestamp(os.path.getmtime(file_path)).strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            # Check if file is compressed
            if file_path.endswith('.gz'):
                info_text += "Type: Compressed backup file (.gz)\n"
            elif file_path.endswith('.db'):
                info_text += "Type: Database backup file (.db)\n"

            # Add more analysis here
            info_text += "\nFile appears to be a valid backup.\n"
            info_text += "Ready for restore operation."

            self.restore_info_text.config(state=tk.NORMAL)
            self.restore_info_text.delete(1.0, tk.END)
            self.restore_info_text.insert(1.0, info_text)
            self.restore_info_text.config(state=tk.DISABLED)

        except Exception as e:
            print(f'[ERROR] Failed to analyze restore file: {e}')
    
    def _start_backup(self):
        """Start the backup process."""
        print('[DEBUG] BackupDialog._start_backup called')
        
        if not self.backup_path.get():
            messagebox.showerror("Backup Error", "Please select a backup location.")
            return
        
        try:
            self.backup_in_progress = True
            self.backup_button.config(state=tk.DISABLED)
            
            # Get backup configuration
            config = {
                'compression': self.compression_enabled.get(),
                'verification': self.verification_enabled.get(),
                'backup_type': 'full'
            }
            
            # Start backup
            success = self.backup_manager.create_backup(self.backup_path.get(), config)
            
            if success:
                messagebox.showinfo("Backup Complete", f"Backup created successfully:\n{self.backup_path.get()}")
                self._load_backup_history()  # Refresh history
            else:
                messagebox.showerror("Backup Error", "Backup failed. Please check the logs.")
            
        except Exception as e:
            messagebox.showerror("Backup Error", f"Backup failed: {e}")
        finally:
            self.backup_in_progress = False
            self.backup_button.config(state=tk.NORMAL)
    
    def _start_restore(self):
        """Start the restore process."""
        print('[DEBUG] BackupDialog._start_restore called')

        if not self.restore_path.get():
            messagebox.showerror("Restore Error", "Please select a backup file to restore.")
            return

        backup_file = self.restore_path.get()
        if not os.path.exists(backup_file):
            messagebox.showerror("Restore Error", f"Backup file not found:\n{backup_file}")
            return

        # Confirm restore
        if not messagebox.askyesno(
            "Confirm Restore",
            f"This will replace all current data with the backup data from:\n{os.path.basename(backup_file)}\n\n"
            "A backup of your current data will be created before restore.\n\n"
            "Are you sure you want to continue?"
        ):
            return

        try:
            # Import RestoreManager
            from backup.restore_manager import RestoreManager

            # Create restore manager and set progress callback
            restore_manager = RestoreManager(self.database_manager)
            restore_manager.set_progress_callback(self._update_progress)

            # Disable restore button during operation
            self.restore_button.config(state=tk.DISABLED)

            # Start restore
            success = restore_manager.restore_from_backup(backup_file, {
                'verification': True,
                'backup_current': True,
                'restore_type': 'full'
            })

            if success:
                messagebox.showinfo(
                    "Restore Complete",
                    f"Database restored successfully from:\n{os.path.basename(backup_file)}\n\n"
                    "Please restart the application to see the restored data."
                )
                # Close the dialog after successful restore
                self.destroy()
            else:
                messagebox.showerror("Restore Error", "Restore failed. Please check the logs.")

        except Exception as e:
            print(f'[ERROR] Restore failed: {e}')
            messagebox.showerror("Restore Error", f"Restore failed: {e}")
        finally:
            self.restore_button.config(state=tk.NORMAL)
    
    def _update_progress(self, percentage: int, message: str):
        """Update progress display."""
        self.progress_var.set(percentage)
        self.progress_label.config(text=message)
        self.update_idletasks()
    
    def _close_dialog(self):
        """Close the dialog."""
        print('[DEBUG] BackupDialog._close_dialog called')
        
        if self.backup_in_progress:
            if messagebox.askyesno("Backup in Progress", "A backup is currently in progress. Cancel it?"):
                self.backup_manager.cancel_backup()
                self.destroy()
        else:
            self.destroy()
