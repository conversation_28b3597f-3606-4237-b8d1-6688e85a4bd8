# ClipsMore Keyboard Shortcuts & Accessibility Guide

## 🚀 Quick Start

Welcome to ClipsMore's comprehensive keyboard shortcuts guide! This guide will help you navigate and use ClipsMore efficiently using only your keyboard, making you more productive and ensuring the application is accessible to all users.

### **Essential Shortcuts for New Users**
- **Ctrl+Z**: Undo last action
- **Tab**: Navigate between interface elements
- **Enter**: Activate buttons or confirm actions
- **Escape**: Cancel current operation or close dialogs
- **F1**: Show help (opens this guide in About tab)

---

## ⌨️ Global Application Shortcuts

These shortcuts work from anywhere in the application:

### **Application Control**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **Ctrl+Q** | Quit Application | Safely close ClipsMore |
| **Ctrl+Z** | Undo | Undo the last action (currently implemented) |
| **Ctrl+Y** | Redo | Redo the last undone action |
| **F1** | Help | Open About tab with documentation |

### **Tab Navigation**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **Ctrl+1** | Clips Tab | Switch to Clips tab |
| **Ctrl+2** | More Tab | Switch to More tab |
| **Ctrl+3** | About Tab | Switch to About tab |
| **Ctrl+Tab** | Next Tab | Move to next tab |
| **Ctrl+Shift+Tab** | Previous Tab | Move to previous tab |

### **Theme & Display**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **Ctrl+T** | Toggle Theme | Switch between light and dark mode |
| **Ctrl+Plus** | Increase Font Size | Make text larger |
| **Ctrl+Minus** | Decrease Font Size | Make text smaller |
| **Ctrl+0** | Reset Font Size | Return to default font size |

---

## 📋 Clips Tab Shortcuts

Navigate and manage your clipboard history efficiently:

### **Navigation & Selection**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **↑/↓** | Navigate Clips | Move up/down through clip list |
| **Home** | First Clip | Jump to the first clip |
| **End** | Last Clip | Jump to the last clip |
| **Page Up** | Page Up | Move up one page of clips |
| **Page Down** | Page Down | Move down one page of clips |
| **Ctrl+A** | Select All | Select all visible clips |
| **Ctrl+F** | Find/Search | Open search functionality |
| **Escape** | Clear Selection | Clear current selection or search |

### **Clip Operations**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **Enter** | Copy to Clipboard | Copy selected clip to system clipboard |
| **Ctrl+C** | Copy Clip | Copy selected clip to system clipboard |
| **Delete** | Delete Clip | Delete selected clip (with confirmation) |
| **F2** | Edit Alias | Edit the alias of selected clip |
| **Ctrl+D** | Duplicate Clip | Create a copy of selected clip |
| **Space** | Toggle Selection | Toggle selection of current clip |

### **Assignment Operations**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **Ctrl+B** | Assign to Business Case | Open business case assignment dialog |
| **Ctrl+M** | Assign to Component | Open component assignment dialog |
| **Ctrl+U** | Unassign | Remove business case/component assignment |
| **Alt+A** | Quick Assign | Open quick assignment dialog |

---

## 🌳 More Tab Shortcuts

Efficiently manage your business cases and components:

### **Tree Navigation**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **↑/↓** | Navigate Items | Move up/down through tree items |
| **←** | Collapse Node | Collapse expanded tree node |
| **→** | Expand Node | Expand collapsed tree node |
| **Home** | First Item | Jump to first tree item |
| **End** | Last Item | Jump to last tree item |
| **Tab** | Switch Focus | Move between tree and input fields |

### **Item Management**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **Ctrl+N** | New Business Case | Create new business case |
| **Ctrl+Shift+N** | New Component | Create new component |
| **F2** | Rename Item | Rename selected business case/component |
| **Delete** | Delete Item | Delete selected item (with confirmation) |
| **Enter** | Confirm Action | Confirm add/rename operation |
| **Escape** | Cancel Action | Cancel current add/rename operation |

### **Organization Operations**
| Shortcut | Action | Description |
|----------|--------|-------------|
| **Ctrl+X** | Cut Item | Cut item for moving |
| **Ctrl+V** | Paste/Move | Paste/move item to selected location |
| **Ctrl+Shift+V** | Copy Item | Copy item to selected location |
| **Ctrl+↑** | Move Up | Move item up in hierarchy |
| **Ctrl+↓** | Move Down | Move item down in hierarchy |

---

## 🔍 Search & Filter Shortcuts

Quickly find what you're looking for:

| Shortcut | Action | Description |
|----------|--------|-------------|
| **Ctrl+F** | Open Search | Open search dialog in current tab |
| **F3** | Find Next | Find next search result |
| **Shift+F3** | Find Previous | Find previous search result |
| **Ctrl+G** | Go To | Go to specific item by name |
| **Ctrl+L** | Clear Filters | Clear all active filters |
| **Alt+F** | Advanced Filter | Open advanced filter dialog |
| **Escape** | Close Search | Close search and clear results |

---

## ♿ Accessibility Features

ClipsMore is designed to be accessible to all users, including those using assistive technologies.

### **Screen Reader Support**
- **Full NVDA Compatibility**: Tested with NVDA screen reader
- **JAWS Support**: Compatible with JAWS screen reader
- **Proper ARIA Labels**: All interactive elements have descriptive labels
- **State Announcements**: Screen readers announce changes in application state
- **Navigation Landmarks**: Proper landmark roles for efficient navigation

### **Visual Accessibility**
- **High Contrast Mode**: Automatic detection and support for system high contrast
- **Focus Indicators**: Clear visual focus indicators for keyboard navigation
- **Color Independence**: No information conveyed through color alone
- **Text Scaling**: Support for system text scaling up to 200%
- **Theme Options**: Light and dark themes for different visual preferences

### **Motor Accessibility**
- **Large Click Targets**: All interactive elements meet 44px minimum size
- **Keyboard Alternatives**: Every mouse operation has a keyboard equivalent
- **No Time Limits**: No time-sensitive operations that cannot be extended
- **Error Prevention**: Confirmation dialogs for destructive operations
- **Undo Functionality**: Comprehensive undo system for error recovery

### **Keyboard Navigation Paths**

#### **Clips Tab Navigation Flow**
1. **Tab** → Search field (if visible)
2. **Tab** → Clip list
3. **↑/↓** → Navigate through clips
4. **Tab** → Assignment dropdowns
5. **Tab** → Action buttons (Assign, Unassign, Delete, Copy)

#### **More Tab Navigation Flow**
1. **Tab** → Tree view
2. **↑/↓** → Navigate tree items
3. **Tab** → Input fields (Business Case/Component name)
4. **Tab** → Action buttons (Add, Rename, Delete)

#### **About Tab Navigation Flow**
1. **Tab** → Documentation tabs
2. **↑/↓** → Navigate through documentation sections
3. **Tab** → Links and interactive elements within documentation

---

## 💡 Power User Tips

### **Workflow Optimization**
- **Quick Clip Access**: Use Ctrl+F to quickly find clips by content or alias
- **Batch Operations**: Use Ctrl+A to select all clips for bulk operations
- **Rapid Assignment**: Use Alt+A for quick assignment without navigating menus
- **Theme Switching**: Use Ctrl+T to quickly switch themes based on lighting conditions

### **Efficiency Techniques**
- **Tab Switching**: Use Ctrl+1/2/3 for instant tab switching instead of mouse clicks
- **Search First**: Always use Ctrl+F to locate items before scrolling
- **Keyboard-Only Workflow**: Challenge yourself to complete tasks without using the mouse
- **Undo Safety Net**: Remember Ctrl+Z is always available to undo mistakes

### **Advanced Combinations**
- **Ctrl+F → Type → Enter**: Quick search and select first result
- **Ctrl+1 → Ctrl+F**: Switch to Clips and immediately search
- **F2 → Type → Enter**: Quick rename workflow
- **Ctrl+N → Type → Enter**: Quick business case creation

### **Customization Options**
- **Theme Preference**: Set your preferred theme and use Ctrl+T when needed
- **Font Size**: Adjust font size with Ctrl+Plus/Minus for comfortable reading
- **High Contrast**: Enable system high contrast mode for better visibility

---

## 🔧 Troubleshooting

### **Common Issues**

**Shortcuts Not Working**
- Ensure ClipsMore window has focus
- Check if another application is intercepting the shortcut
- Try clicking in the application area first

**Screen Reader Issues**
- Ensure your screen reader is running before starting ClipsMore
- Update to the latest version of your screen reader software
- Check screen reader settings for application-specific configurations

**Focus Problems**
- Use Tab to navigate to the desired area
- Press Escape to reset focus if navigation seems stuck
- Restart ClipsMore if focus management becomes unresponsive

**High Contrast Mode**
- Enable Windows High Contrast mode in system settings
- Restart ClipsMore after changing system contrast settings
- Use Ctrl+T to switch themes if contrast is insufficient

### **Getting Help**
- **F1**: Open this guide anytime
- **About Tab**: Access full documentation and system information
- **Error Messages**: Read error messages carefully - they often contain solution hints

---

## 📚 Quick Reference Card

Print or bookmark this quick reference for easy access:

### **Essential Shortcuts**
- **Ctrl+1/2/3**: Switch tabs
- **Ctrl+F**: Search
- **Ctrl+Z**: Undo
- **F2**: Rename
- **Delete**: Delete item
- **Enter**: Confirm/Copy
- **Escape**: Cancel
- **Tab**: Navigate
- **Ctrl+T**: Toggle theme

### **Accessibility**
- **Tab Order**: Logical navigation flow
- **Screen Readers**: Full NVDA/JAWS support
- **High Contrast**: Automatic system detection
- **Keyboard Only**: 100% keyboard accessible
- **Focus Indicators**: Clear visual feedback

---

*This guide covers ClipsMore v2.0+ keyboard shortcuts and accessibility features. For additional help, visit the About tab or check the User Guide.*
