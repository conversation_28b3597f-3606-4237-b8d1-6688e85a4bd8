#!/usr/bin/env python3
"""
Demonstration test for the smart file browser feature.
Shows how the browse button intelligently opens to target locations.
"""

import os
import sys
import tkinter as tk
from unittest.mock import patch
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backup.backup_dialog import BackupDialog


def demonstrate_smart_browse():
    """Demonstrate the smart file browser functionality."""
    print('=' * 70)
    print('SMART FILE BROWSER DEMONSTRATION')
    print('=' * 70)
    
    try:
        # Create root window and dialog
        root = tk.Tk()
        root.withdraw()
        
        dialog = BackupDialog(root)
        
        print('\n📁 SCENARIO 1: First-time user (no backup path set)')
        print('Expected: Opens to Documents folder with default filename')
        
        dialog.backup_path.set("")
        
        with patch('tkinter.filedialog.asksaveasfilename') as mock_dialog:
            mock_dialog.return_value = ""  # Simulate user cancel
            
            dialog._browse_backup_path()
            
            call_args = mock_dialog.call_args
            if call_args:
                kwargs = call_args[1]
                print(f'✅ Opens to: {kwargs.get("initialdir")}')
                print(f'✅ Default filename: {kwargs.get("initialfile")}')
        
        print('\n📁 SCENARIO 2: User has previously set a backup location')
        print('Expected: Opens to the same directory as the existing path')
        
        existing_path = r"C:\Users\<USER>\Desktop\my_backups\previous_backup.db"
        dialog.backup_path.set(existing_path)
        
        with patch('tkinter.filedialog.asksaveasfilename') as mock_dialog:
            mock_dialog.return_value = ""  # Simulate user cancel
            
            dialog._browse_backup_path()
            
            call_args = mock_dialog.call_args
            if call_args:
                kwargs = call_args[1]
                print(f'✅ Opens to: {kwargs.get("initialdir")}')
                print(f'✅ Suggests filename: {kwargs.get("initialfile")}')
        
        print('\n📁 SCENARIO 3: User clicks Desktop quick button then Browse')
        print('Expected: Opens to Desktop folder')
        
        # Simulate clicking Desktop button
        dialog._set_quick_location("Desktop")
        current_path = dialog.backup_path.get()
        print(f'Quick location set path to: {current_path}')
        
        with patch('tkinter.filedialog.asksaveasfilename') as mock_dialog:
            mock_dialog.return_value = ""  # Simulate user cancel
            
            dialog._browse_backup_path()
            
            call_args = mock_dialog.call_args
            if call_args:
                kwargs = call_args[1]
                print(f'✅ Opens to: {kwargs.get("initialdir")}')
                print(f'✅ Suggests filename: {kwargs.get("initialfile")}')
        
        print('\n📁 SCENARIO 4: User selects a new backup file')
        print('Expected: Path updates and subsequent browse opens to that location')
        
        new_backup_path = r"C:\Users\<USER>\Documents\ClipsMore\Backups\new_backup.db"
        
        with patch('tkinter.filedialog.asksaveasfilename') as mock_dialog:
            mock_dialog.return_value = new_backup_path
            
            dialog._browse_backup_path()
            
            print(f'✅ Path updated to: {dialog.backup_path.get()}')
        
        # Now browse again to show it remembers the location
        with patch('tkinter.filedialog.asksaveasfilename') as mock_dialog:
            mock_dialog.return_value = ""  # Simulate user cancel
            
            dialog._browse_backup_path()
            
            call_args = mock_dialog.call_args
            if call_args:
                kwargs = call_args[1]
                print(f'✅ Next browse opens to: {kwargs.get("initialdir")}')
        
        # Clean up
        dialog.destroy()
        root.destroy()
        
        print('\n' + '=' * 70)
        print('🎯 SMART FILE BROWSER BENEFITS:')
        print('  • No more navigating from random locations')
        print('  • Remembers user preferences and previous locations')
        print('  • Intelligent fallback hierarchy for reliability')
        print('  • Consistent with quick location buttons')
        print('  • Generates meaningful default filenames')
        print('=' * 70)
        
        return True
        
    except Exception as e:
        print(f'[ERROR] Demonstration failed: {e}')
        import traceback
        traceback.print_exc()
        return False


def show_directory_hierarchy():
    """Show the directory detection hierarchy."""
    print('\n🔍 DIRECTORY DETECTION HIERARCHY:')
    print('1. If backup path already set → Use that directory')
    print('2. Else try Documents folder → Most common user preference')
    print('3. Else try Desktop folder → Visible and accessible')
    print('4. Else use current working directory → Always available')
    
    from utils.directory_manager import DirectoryManager
    
    dm = DirectoryManager()
    
    try:
        docs = dm.get_documents_path()
        docs_valid = dm.validate_directory(str(docs))
        print(f'   Documents: {docs} ({"✅ Valid" if docs_valid else "❌ Invalid"})')
    except:
        print('   Documents: ❌ Not detected')
    
    try:
        desktop = dm.get_desktop_path()
        desktop_valid = dm.validate_directory(str(desktop))
        print(f'   Desktop: {desktop} ({"✅ Valid" if desktop_valid else "❌ Invalid"})')
    except:
        print('   Desktop: ❌ Not detected')
    
    cwd = os.getcwd()
    print(f'   Current Dir: {cwd} (✅ Fallback)')


if __name__ == '__main__':
    success = demonstrate_smart_browse()
    show_directory_hierarchy()
    
    if success:
        print('\n🎉 SMART FILE BROWSER DEMONSTRATION COMPLETED!')
        print('\nThe browse button now intelligently opens to the target location,')
        print('making it much easier for users to save backups where they want them.')
        sys.exit(0)
    else:
        print('\n❌ DEMONSTRATION FAILED!')
        sys.exit(1)
