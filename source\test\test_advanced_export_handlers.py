#!/usr/bin/env python3
"""
Test suite for Advanced Export Handlers (HTML and XML)
Tests the new HTML and XML export functionality.
"""

import unittest
import tempfile
import os
import sys
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from export.format_handlers.html_handler import HTMLHandler
from export.format_handlers.xml_handler import XMLHandler


class TestHTMLHandler(unittest.TestCase):
    """Test cases for HTML export handler."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestHTMLHandler.setUp called')
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
        # Initialize HTML handler
        self.handler = HTMLHandler()
        
        # Sample test data
        self.test_data = [
            {
                'clip_id': 1,
                'alias': 'test_clip_1',
                'content': 'This is test content for clip 1',
                'timestamp': '2024-01-01T10:00:00',
                'bus_case': 'Test Business Case',
                'bus_component': 'Test Component',
                'more_bus_id': 1,
                'more_comp_id': 1
            },
            {
                'clip_id': 2,
                'alias': 'test_clip_2',
                'content': 'This is test content for clip 2\nWith multiple lines',
                'timestamp': '2024-01-01T11:00:00',
                'bus_case': 'Test Business Case',
                'bus_component': 'Another Component',
                'more_bus_id': 1,
                'more_comp_id': 2
            },
            {
                'clip_id': 3,
                'alias': 'test_clip_3',
                'content': 'This is test content for clip 3',
                'timestamp': '2024-01-01T12:00:00',
                'bus_case': 'Another Business Case',
                'bus_component': None,
                'more_bus_id': 2,
                'more_comp_id': None
            }
        ]
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestHTMLHandler.tearDown called')
        
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_html_handler_initialization(self):
        """Test HTML handler initialization."""
        print('[DEBUG] test_html_handler_initialization called')
        
        self.assertIsNotNone(self.handler)
        self.assertEqual(self.handler.format_type, 'html')
        self.assertEqual(self.handler.file_extension, '.html')
        self.assertEqual(self.handler.get_format_name(), 'HTML')
    
    def test_html_export_basic(self):
        """Test basic HTML export functionality."""
        print('[DEBUG] test_html_export_basic called')
        
        output_path = os.path.join(self.temp_dir, 'test_export.html')
        
        success = self.handler.export(self.test_data, output_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(output_path))
        
        # Verify file content
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn('<!DOCTYPE html>', content)
        self.assertIn('ClipsMore Export', content)
        self.assertIn('test_clip_1', content)
        self.assertIn('Test Business Case', content)
    
    def test_html_export_with_themes(self):
        """Test HTML export with different themes."""
        print('[DEBUG] test_html_export_with_themes called')
        
        themes = ['light', 'dark', 'blue']
        
        for theme in themes:
            output_path = os.path.join(self.temp_dir, f'test_export_{theme}.html')
            config = {'theme': theme}
            
            success = self.handler.export(self.test_data, output_path, config)
            self.assertTrue(success)
            self.assertTrue(os.path.exists(output_path))
            
            # Verify theme-specific content
            with open(output_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.assertIn('<style>', content)
            if theme == 'dark':
                self.assertIn('background: #1a1a1a', content)
            elif theme == 'blue':
                self.assertIn('background: #f0f8ff', content)
    
    def test_html_export_grouped_content(self):
        """Test HTML export with grouped content."""
        print('[DEBUG] test_html_export_grouped_content called')
        
        output_path = os.path.join(self.temp_dir, 'test_grouped.html')
        config = {'group_by_business_case': True}
        
        success = self.handler.export(self.test_data, output_path, config)
        self.assertTrue(success)
        
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn('business-case-title', content)
        self.assertIn('component-title', content)
    
    def test_html_export_linear_content(self):
        """Test HTML export with linear content."""
        print('[DEBUG] test_html_export_linear_content called')
        
        output_path = os.path.join(self.temp_dir, 'test_linear.html')
        config = {'group_by_business_case': False}
        
        success = self.handler.export(self.test_data, output_path, config)
        self.assertTrue(success)
        
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Should not have business case grouping elements (CSS classes are still included)
        self.assertNotIn('<div class="business-case-title">', content)
        self.assertNotIn('<div class="component-title">', content)


class TestXMLHandler(unittest.TestCase):
    """Test cases for XML export handler."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestXMLHandler.setUp called')
        
        # Create temporary directory for test outputs
        self.temp_dir = tempfile.mkdtemp()
        
        # Initialize XML handler
        self.handler = XMLHandler()
        
        # Sample test data
        self.test_data = [
            {
                'clip_id': 1,
                'transaction_id': 101,
                'alias': 'test_clip_1',
                'content': 'This is test content for clip 1',
                'timestamp': '2024-01-01T10:00:00',
                'bus_case': 'Test Business Case',
                'bus_component': 'Test Component',
                'more_bus_id': 1,
                'more_comp_id': 1,
                'created_date': '2024-01-01T09:00:00',
                'modified_date': '2024-01-01T10:00:00',
                'tree_position': 0
            },
            {
                'clip_id': 2,
                'transaction_id': 102,
                'alias': 'test_clip_2',
                'content': 'This is test content for clip 2',
                'timestamp': '2024-01-01T11:00:00',
                'bus_case': 'Test Business Case',
                'bus_component': 'Another Component',
                'more_bus_id': 1,
                'more_comp_id': 2
            }
        ]
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestXMLHandler.tearDown called')
        
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_xml_handler_initialization(self):
        """Test XML handler initialization."""
        print('[DEBUG] test_xml_handler_initialization called')
        
        self.assertIsNotNone(self.handler)
        self.assertEqual(self.handler.format_type, 'xml')
        self.assertEqual(self.handler.file_extension, '.xml')
        self.assertEqual(self.handler.get_format_name(), 'XML')
        self.assertEqual(self.handler.schema_version, '1.0')
    
    def test_xml_export_basic(self):
        """Test basic XML export functionality."""
        print('[DEBUG] test_xml_export_basic called')
        
        output_path = os.path.join(self.temp_dir, 'test_export.xml')
        
        success = self.handler.export(self.test_data, output_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(output_path))
        
        # Verify file content
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn('<?xml version=', content)
        self.assertIn('<clipsmore-export', content)
        self.assertIn('<clips count="2">', content)
        self.assertIn('<alias>test_clip_1</alias>', content)
        self.assertIn('<content length="31">This is test content for clip 1</content>', content)
    
    def test_xml_export_with_namespace(self):
        """Test XML export with namespace."""
        print('[DEBUG] test_xml_export_with_namespace called')
        
        output_path = os.path.join(self.temp_dir, 'test_namespace.xml')
        config = {'use_namespace': True}
        
        success = self.handler.export(self.test_data, output_path, config)
        self.assertTrue(success)
        
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn('xmlns="http://clipsmore.app/export/v1"', content)
    
    def test_xml_export_without_namespace(self):
        """Test XML export without namespace."""
        print('[DEBUG] test_xml_export_without_namespace called')
        
        output_path = os.path.join(self.temp_dir, 'test_no_namespace.xml')
        config = {'use_namespace': False}
        
        success = self.handler.export(self.test_data, output_path, config)
        self.assertTrue(success)
        
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertNotIn('xmlns=', content)
    
    def test_xml_export_with_metadata(self):
        """Test XML export with metadata."""
        print('[DEBUG] test_xml_export_with_metadata called')
        
        output_path = os.path.join(self.temp_dir, 'test_metadata.xml')
        config = {'include_metadata': True}
        
        success = self.handler.export(self.test_data, output_path, config)
        self.assertTrue(success)
        
        with open(output_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn('<metadata>', content)
        self.assertIn('<export-info>', content)
        self.assertIn('<statistics>', content)
        self.assertIn('<total-clips>2</total-clips>', content)
        self.assertIn('<business-cases>1</business-cases>', content)
    
    def test_xml_validation(self):
        """Test XML validation functionality."""
        print('[DEBUG] test_xml_validation called')
        
        output_path = os.path.join(self.temp_dir, 'test_validation.xml')
        config = {'validate_xml': True}
        
        success = self.handler.export(self.test_data, output_path, config)
        self.assertTrue(success)
        
        # Test validation method directly
        is_valid = self.handler._validate_xml_structure(output_path)
        self.assertTrue(is_valid)
    
    def test_xml_schema_creation(self):
        """Test XSD schema file creation."""
        print('[DEBUG] test_xml_schema_creation called')
        
        schema_path = os.path.join(self.temp_dir, 'clipsmore-export-v1.xsd')
        
        success = self.handler.create_schema_file(schema_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(schema_path))
        
        # Verify schema content
        with open(schema_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn('<?xml version="1.0" encoding="UTF-8"?>', content)
        self.assertIn('<xs:schema', content)
        self.assertIn('targetNamespace="http://clipsmore.app/export/v1"', content)
        self.assertIn('<xs:element name="clipsmore-export">', content)


def run_advanced_export_tests():
    """Run all advanced export handler tests."""
    print("🧪 Running Advanced Export Handler Tests")
    print("=" * 50)

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestHTMLHandler))
    suite.addTests(loader.loadTestsFromTestCase(TestXMLHandler))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print("\n" + "=" * 50)
    print("📊 ADVANCED EXPORT TEST SUMMARY")
    print("=" * 50)

    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors

    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failures}")
    print(f"Errors: {errors}")

    if failures == 0 and errors == 0:
        print("🎉 ALL ADVANCED EXPORT TESTS PASSED!")
        return True
    else:
        print("❌ SOME ADVANCED EXPORT TESTS FAILED!")
        return False


if __name__ == "__main__":
    run_advanced_export_tests()
