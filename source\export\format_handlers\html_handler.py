#!/usr/bin/env python3
"""
HTML Export Handler for ClipsMore Application
Handles exporting clipboard data to HTML format with styling and formatting.
"""

import os
import html
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path


class HTMLHandler:
    """
    HTML format handler for export operations.
    Creates formatted HTML files with CSS styling and hierarchical display.
    """
    
    def __init__(self):
        """Initialize the HTML handler."""
        print('[DEBUG] HTMLHandler.__init__ called')
        self.format_type = 'html'
        self.file_extension = '.html'
    
    def export(self, data: List[Dict[str, Any]], output_path: str, 
               export_config: Dict[str, Any] = None) -> bool:
        """
        Export data to HTML format.
        
        Args:
            data: List of dictionaries containing clip data
            output_path: Output file path
            export_config: Export configuration options
                - include_css: Include embedded CSS styling (default: True)
                - theme: Color theme ('light', 'dark', 'blue') (default: 'light')
                - show_metadata: Include metadata section (default: True)
                - group_by_business_case: Group clips by business case (default: True)
                - include_timestamps: Show timestamps (default: True)
                - print_friendly: Optimize for printing (default: False)
        
        Returns:
            True if export successful, False otherwise
        """
        print(f'[DEBUG] HTMLHandler.export called for {len(data)} records')
        
        if export_config is None:
            export_config = {}
        
        try:
            # Prepare HTML content
            html_content = self._create_html_document(data, export_config)
            
            # Write to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f'[DEBUG] HTML export completed: {output_path}')
            return True
            
        except Exception as e:
            print(f'[ERROR] HTML export failed: {e}')
            return False
    
    def _create_html_document(self, data: List[Dict[str, Any]], 
                            config: Dict[str, Any]) -> str:
        """Create complete HTML document."""
        print('[DEBUG] HTMLHandler._create_html_document called')
        
        # Configuration defaults
        include_css = config.get('include_css', True)
        theme = config.get('theme', 'light')
        show_metadata = config.get('show_metadata', True)
        group_by_business_case = config.get('group_by_business_case', True)
        include_timestamps = config.get('include_timestamps', True)
        print_friendly = config.get('print_friendly', False)
        
        # Build HTML document
        html_parts = []
        
        # HTML header
        html_parts.append(self._create_html_header(theme, include_css, print_friendly))
        
        # Body start
        html_parts.append('<body>')
        html_parts.append('<div class="container">')
        
        # Title and metadata
        html_parts.append(self._create_title_section())
        
        if show_metadata:
            html_parts.append(self._create_metadata_section(data, config))
        
        # Content
        if group_by_business_case:
            html_parts.append(self._create_grouped_content(data, include_timestamps))
        else:
            html_parts.append(self._create_linear_content(data, include_timestamps))
        
        # Footer
        html_parts.append(self._create_footer())
        
        # Close body and html
        html_parts.append('</div>')
        html_parts.append('</body>')
        html_parts.append('</html>')
        
        return '\n'.join(html_parts)
    
    def _create_html_header(self, theme: str, include_css: bool, print_friendly: bool) -> str:
        """Create HTML header with CSS."""
        print('[DEBUG] HTMLHandler._create_html_header called')
        
        header = [
            '<!DOCTYPE html>',
            '<html lang="en">',
            '<head>',
            '    <meta charset="UTF-8">',
            '    <meta name="viewport" content="width=device-width, initial-scale=1.0">',
            '    <title>ClipsMore Export</title>'
        ]
        
        if include_css:
            header.append('    <style>')
            header.append(self._get_css_styles(theme, print_friendly))
            header.append('    </style>')
        
        header.append('</head>')
        
        return '\n'.join(header)
    
    def _get_css_styles(self, theme: str, print_friendly: bool) -> str:
        """Get CSS styles for the HTML document."""
        print(f'[DEBUG] HTMLHandler._get_css_styles called with theme: {theme}')
        
        # Base styles
        css = """
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid;
        }
        
        .metadata {
            background: rgba(0,0,0,0.05);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .business-case {
            margin-bottom: 40px;
        }
        
        .business-case-title {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
        }
        
        .component-group {
            margin-left: 20px;
            margin-bottom: 25px;
        }
        
        .component-title {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 3px;
        }
        
        .clip {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .clip-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .clip-alias {
            font-weight: bold;
            font-size: 1.1em;
        }
        
        .clip-timestamp {
            font-size: 0.9em;
            opacity: 0.7;
        }
        
        .clip-content {
            white-space: pre-wrap;
            word-wrap: break-word;
            background: rgba(0,0,0,0.02);
            padding: 10px;
            border-radius: 3px;
            border: 1px solid rgba(0,0,0,0.1);
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid;
            font-size: 0.9em;
            opacity: 0.7;
        }
        """
        
        # Theme-specific colors
        if theme == 'dark':
            css += """
            body { background: #1a1a1a; color: #e0e0e0; }
            .header { border-bottom-color: #444; }
            .metadata { background: rgba(255,255,255,0.05); }
            .business-case-title { background: #2d2d2d; color: #fff; }
            .component-title { background: #3a3a3a; color: #fff; }
            .clip { background: #2a2a2a; border-left-color: #0078d4; }
            .clip-content { background: rgba(255,255,255,0.05); border-color: #444; }
            .footer { border-top-color: #444; }
            """
        elif theme == 'blue':
            css += """
            body { background: #f0f8ff; color: #1a1a1a; }
            .header { border-bottom-color: #0078d4; color: #0078d4; }
            .metadata { background: rgba(0,120,212,0.1); }
            .business-case-title { background: #0078d4; color: white; }
            .component-title { background: #106ebe; color: white; }
            .clip { background: #fff; border-left-color: #0078d4; }
            .clip-content { background: rgba(0,120,212,0.05); border-color: #0078d4; }
            .footer { border-top-color: #0078d4; }
            """
        else:  # light theme
            css += """
            body { background: #fff; color: #1a1a1a; }
            .header { border-bottom-color: #ddd; }
            .metadata { background: rgba(0,0,0,0.05); }
            .business-case-title { background: #f5f5f5; color: #333; }
            .component-title { background: #e9e9e9; color: #333; }
            .clip { background: #fafafa; border-left-color: #007acc; }
            .clip-content { background: rgba(0,0,0,0.02); border-color: #ddd; }
            .footer { border-top-color: #ddd; }
            """
        
        # Print-friendly styles
        if print_friendly:
            css += """
            @media print {
                body { background: white !important; color: black !important; }
                .container { max-width: none; }
                .clip { page-break-inside: avoid; }
                .business-case { page-break-inside: avoid; }
            }
            """
        
        return css
    
    def _create_title_section(self) -> str:
        """Create title section."""
        return f"""
        <div class="header">
            <h1>ClipsMore Export</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        """
    
    def _create_metadata_section(self, data: List[Dict[str, Any]], 
                                config: Dict[str, Any]) -> str:
        """Create metadata section."""
        print('[DEBUG] HTMLHandler._create_metadata_section called')
        
        total_clips = len(data)
        business_cases = set()
        components = set()
        
        for record in data:
            if record.get('bus_case'):
                business_cases.add(record['bus_case'])
            if record.get('bus_component'):
                components.add(record['bus_component'])
        
        metadata = f"""
        <div class="metadata">
            <h3>Export Summary</h3>
            <ul>
                <li><strong>Total Clips:</strong> {total_clips}</li>
                <li><strong>Business Cases:</strong> {len(business_cases)}</li>
                <li><strong>Components:</strong> {len(components)}</li>
                <li><strong>Export Format:</strong> HTML</li>
                <li><strong>Theme:</strong> {config.get('theme', 'light').title()}</li>
            </ul>
        </div>
        """
        
        return metadata
    
    def _create_grouped_content(self, data: List[Dict[str, Any]], 
                              include_timestamps: bool) -> str:
        """Create content grouped by business case and component."""
        print('[DEBUG] HTMLHandler._create_grouped_content called')
        
        # Group data by business case and component
        grouped = {}
        
        for record in data:
            bus_case = record.get('bus_case', 'Unassigned')
            bus_component = record.get('bus_component', 'No Component')
            
            if bus_case not in grouped:
                grouped[bus_case] = {}
            
            if bus_component not in grouped[bus_case]:
                grouped[bus_case][bus_component] = []
            
            grouped[bus_case][bus_component].append(record)
        
        # Generate HTML
        content_parts = ['<div class="content">']
        
        for bus_case, components in sorted(grouped.items()):
            content_parts.append(f'<div class="business-case">')
            content_parts.append(f'<div class="business-case-title">{html.escape(str(bus_case))}</div>')

            for component, clips in sorted(components.items()):
                if component != 'No Component':
                    content_parts.append(f'<div class="component-group">')
                    content_parts.append(f'<div class="component-title">{html.escape(str(component))}</div>')

                for clip in clips:
                    content_parts.append(self._create_clip_html(clip, include_timestamps))

                if component != 'No Component':
                    content_parts.append('</div>')

            content_parts.append('</div>')
        
        content_parts.append('</div>')
        
        return '\n'.join(content_parts)
    
    def _create_linear_content(self, data: List[Dict[str, Any]], 
                             include_timestamps: bool) -> str:
        """Create linear content without grouping."""
        print('[DEBUG] HTMLHandler._create_linear_content called')
        
        content_parts = ['<div class="content">']
        
        for record in data:
            content_parts.append(self._create_clip_html(record, include_timestamps))
        
        content_parts.append('</div>')
        
        return '\n'.join(content_parts)
    
    def _create_clip_html(self, record: Dict[str, Any], include_timestamps: bool) -> str:
        """Create HTML for a single clip."""
        alias = html.escape(str(record.get('alias', f"Clip {record.get('clip_id', 'Unknown')}")))
        content = html.escape(str(record.get('content', '')))
        timestamp = record.get('timestamp', '')

        clip_html = ['<div class="clip">']

        # Clip header
        clip_html.append('<div class="clip-header">')
        clip_html.append(f'<span class="clip-alias">{alias}</span>')

        if include_timestamps and timestamp:
            clip_html.append(f'<span class="clip-timestamp">{html.escape(str(timestamp))}</span>')

        clip_html.append('</div>')

        # Clip content
        clip_html.append(f'<div class="clip-content">{content}</div>')

        clip_html.append('</div>')

        return '\n'.join(clip_html)
    
    def _create_footer(self) -> str:
        """Create footer section."""
        return f"""
        <div class="footer">
            <p>Exported from ClipsMore - Clipboard Management System</p>
        </div>
        """
    
    def get_format_name(self) -> str:
        """Get human-readable format name."""
        return "HTML"
    
    def get_file_extension(self) -> str:
        """Get file extension for this format."""
        return self.file_extension
    
    def get_format_description(self) -> str:
        """Get format description."""
        return "HTML files with CSS styling and hierarchical display"
