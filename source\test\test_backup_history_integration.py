#!/usr/bin/env python3
"""
Test script for backup history integration feature.
Tests the new functionality where clicking backup history items populates the restore path.
"""

import os
import sys
import tempfile
import shutil
import sqlite3
import tkinter as tk
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backup.backup_manager import BackupManager
from backup.backup_dialog import BackupDialog
from DB.db_connection import Connection<PERSON>oolManager


def test_backup_history_integration():
    """Test the backup history to restore path integration."""
    print('[TEST] Starting backup history integration test')
    
    # Create temporary directory for test
    test_dir = tempfile.mkdtemp(prefix='clipmore_history_test_')
    print(f'[TEST] Using test directory: {test_dir}')
    
    try:
        # Initialize backup manager
        backup_manager = BackupManager()
        
        # Create a test backup
        backup_filename = f"test_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join(test_dir, backup_filename)
        
        print('[TEST] Creating test backup...')
        backup_success = backup_manager.create_backup(backup_path, {
            'compression': False,
            'verification': True
        })
        
        if not backup_success:
            print('[ERROR] Test backup creation failed')
            return False
        
        print(f'[TEST] Test backup created: {backup_path}')
        
        # Verify backup is in history
        history = backup_manager.get_backup_history()
        backup_found = False
        for backup in history:
            if backup.get('backup_path') == backup_path:
                backup_found = True
                print(f'[TEST] Backup found in history: {backup}')
                break
        
        if not backup_found:
            print('[ERROR] Backup not found in history')
            return False
        
        print('[TEST] Backup history integration test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        return False
        
    finally:
        # Clean up test directory
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f'[TEST] Cleaned up test directory: {test_dir}')


def test_backup_dialog_ui():
    """Test the backup dialog UI with the new history integration."""
    print('[TEST] Testing backup dialog UI')
    
    try:
        # Create root window
        root = tk.Tk()
        root.withdraw()  # Hide the root window
        
        # Create backup dialog
        dialog = BackupDialog(root)
        
        # Test that the dialog has the required components
        if not hasattr(dialog, 'history_tree'):
            print('[ERROR] Dialog missing history_tree')
            return False
        
        if not hasattr(dialog, 'restore_path'):
            print('[ERROR] Dialog missing restore_path')
            return False
        
        if not hasattr(dialog, 'restore_button'):
            print('[ERROR] Dialog missing restore_button')
            return False
        
        # Test that click handlers are bound
        bindings = dialog.history_tree.bind()
        if '<Button-1>' not in bindings:
            print('[ERROR] Single click handler not bound to history tree')
            return False
        
        if '<Double-Button-1>' not in bindings:
            print('[ERROR] Double click handler not bound to history tree')
            return False
        
        print('[TEST] All required UI components found')
        
        # Test the click handler methods exist
        if not hasattr(dialog, '_on_history_click'):
            print('[ERROR] _on_history_click method missing')
            return False
        
        if not hasattr(dialog, '_on_history_double_click'):
            print('[ERROR] _on_history_double_click method missing')
            return False
        
        if not hasattr(dialog, '_show_backup_details'):
            print('[ERROR] _show_backup_details method missing')
            return False
        
        if not hasattr(dialog, '_use_backup_for_restore'):
            print('[ERROR] _use_backup_for_restore method missing')
            return False
        
        print('[TEST] All required methods found')
        
        # Clean up
        dialog.destroy()
        root.destroy()
        
        print('[TEST] Backup dialog UI test passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] UI test failed with exception: {e}')
        return False


def test_restore_functionality():
    """Test the restore functionality integration."""
    print('[TEST] Testing restore functionality')
    
    try:
        # Test that RestoreManager can be imported
        from backup.restore_manager import RestoreManager
        
        # Create restore manager
        restore_manager = RestoreManager()
        
        # Test that required methods exist
        if not hasattr(restore_manager, 'restore_from_backup'):
            print('[ERROR] restore_from_backup method missing')
            return False
        
        if not hasattr(restore_manager, 'set_progress_callback'):
            print('[ERROR] set_progress_callback method missing')
            return False
        
        print('[TEST] RestoreManager functionality verified')
        return True
        
    except Exception as e:
        print(f'[ERROR] Restore functionality test failed: {e}')
        return False


if __name__ == '__main__':
    print('=' * 60)
    print('BACKUP HISTORY INTEGRATION TEST')
    print('=' * 60)
    
    # Run tests
    test1_passed = test_backup_history_integration()
    test2_passed = test_backup_dialog_ui()
    test3_passed = test_restore_functionality()
    
    print('=' * 60)
    print('TEST RESULTS:')
    print(f'  Backup History Integration: {"PASSED" if test1_passed else "FAILED"}')
    print(f'  Backup Dialog UI: {"PASSED" if test2_passed else "FAILED"}')
    print(f'  Restore Functionality: {"PASSED" if test3_passed else "FAILED"}')
    
    if test1_passed and test2_passed and test3_passed:
        print('  OVERALL: ALL TESTS PASSED ✅')
        print('\n📋 NEW FEATURE SUMMARY:')
        print('  • Click any backup in History tab to auto-populate restore path')
        print('  • Double-click backup for detailed information')
        print('  • Seamless integration between History and Restore tabs')
        print('  • Full restore functionality with progress tracking')
        sys.exit(0)
    else:
        print('  OVERALL: SOME TESTS FAILED ❌')
        sys.exit(1)
