#!/usr/bin/env python3
"""
Test suite for ClipsMore Import System
Tests import functionality, format parsers, and duplicate handling.
"""

import unittest
import tempfile
import os
import json
import csv
import sys
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import using importlib to avoid keyword conflicts
import importlib.util
import sys

# Load import modules
spec = importlib.util.spec_from_file_location("import_manager",
    os.path.join(os.path.dirname(os.path.dirname(__file__)), "import", "import_manager.py"))
import_manager_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(import_manager_module)

spec = importlib.util.spec_from_file_location("json_parser",
    os.path.join(os.path.dirname(os.path.dirname(__file__)), "import", "format_parsers", "json_parser.py"))
json_parser_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(json_parser_module)

spec = importlib.util.spec_from_file_location("csv_parser",
    os.path.join(os.path.dirname(os.path.dirname(__file__)), "import", "format_parsers", "csv_parser.py"))
csv_parser_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(csv_parser_module)

spec = importlib.util.spec_from_file_location("duplicate_handler",
    os.path.join(os.path.dirname(os.path.dirname(__file__)), "import", "duplicate_handler.py"))
duplicate_handler_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(duplicate_handler_module)

# Extract classes
ImportManager = import_manager_module.ImportManager
ImportConfig = import_manager_module.ImportConfig
ImportError = import_manager_module.ImportError
JSONParser = json_parser_module.JSONParser
JSONParseError = json_parser_module.JSONParseError
CSVParser = csv_parser_module.CSVParser
CSVParseError = csv_parser_module.CSVParseError
DuplicateHandler = duplicate_handler_module.DuplicateHandler


class TestImportSystem(unittest.TestCase):
    """Test cases for the import system."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestImportSystem.setUp called')
        
        # Create temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        
        # Initialize import manager
        self.import_manager = ImportManager()
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestImportSystem.tearDown called')
        
        # Clean up temporary files
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_import_manager_initialization(self):
        """Test import manager initialization."""
        print('[DEBUG] test_import_manager_initialization called')
        
        self.assertIsNotNone(self.import_manager)
        self.assertIsNotNone(self.import_manager.connection_pool)
        self.assertIn('json', self.import_manager.get_available_formats())
        self.assertIn('csv', self.import_manager.get_available_formats())
    
    def test_format_detection(self):
        """Test file format detection."""
        print('[DEBUG] test_format_detection called')
        
        # Test JSON detection
        json_file = os.path.join(self.temp_dir, 'test.json')
        with open(json_file, 'w') as f:
            json.dump({'test': 'data'}, f)
        
        self.assertEqual(self.import_manager.detect_format(json_file), 'json')
        
        # Test CSV detection
        csv_file = os.path.join(self.temp_dir, 'test.csv')
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['header1', 'header2'])
            writer.writerow(['data1', 'data2'])
        
        self.assertEqual(self.import_manager.detect_format(csv_file), 'csv')
    
    def test_file_validation(self):
        """Test import file validation."""
        print('[DEBUG] test_file_validation called')
        
        # Test valid file
        valid_file = os.path.join(self.temp_dir, 'valid.json')
        with open(valid_file, 'w') as f:
            json.dump({'test': 'data'}, f)
        
        is_valid, error_msg = self.import_manager.validate_import_file(valid_file)
        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")
        
        # Test non-existent file
        non_existent = os.path.join(self.temp_dir, 'nonexistent.json')
        is_valid, error_msg = self.import_manager.validate_import_file(non_existent)
        self.assertFalse(is_valid)
        self.assertIn("does not exist", error_msg)
        
        # Test empty file
        empty_file = os.path.join(self.temp_dir, 'empty.json')
        with open(empty_file, 'w') as f:
            pass  # Create empty file
        
        is_valid, error_msg = self.import_manager.validate_import_file(empty_file)
        self.assertFalse(is_valid)
        self.assertIn("empty", error_msg)


class TestJSONParser(unittest.TestCase):
    """Test cases for JSON parser."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestJSONParser.setUp called')
        
        self.temp_dir = tempfile.mkdtemp()
        self.parser = JSONParser()
        self.config = ImportConfig()
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestJSONParser.tearDown called')
        
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_json_parser_initialization(self):
        """Test JSON parser initialization."""
        print('[DEBUG] test_json_parser_initialization called')
        
        self.assertIsNotNone(self.parser)
        self.assertIn('clipsmore_v1', self.parser.supported_schemas)
        self.assertIn('generic', self.parser.supported_schemas)
    
    def test_clipsmore_format_detection(self):
        """Test ClipsMore format detection."""
        print('[DEBUG] test_clipsmore_format_detection called')
        
        # ClipsMore export format
        clipsmore_data = {
            'metadata': {
                'source': 'ClipsMore',
                'version': '1.0',
                'export_date': '2024-01-01T10:00:00'
            },
            'data': [
                {'clip_id': 1, 'content': 'Test content', 'alias': 'test'}
            ]
        }
        
        schema = self.parser.detect_schema(clipsmore_data)
        self.assertEqual(schema, 'clipsmore_v1')
        
        # Generic format
        generic_data = [
            {'field1': 'value1', 'field2': 'value2'}
        ]
        
        schema = self.parser.detect_schema(generic_data)
        self.assertEqual(schema, 'generic')
    
    def test_json_preview(self):
        """Test JSON file preview functionality."""
        print('[DEBUG] test_json_preview called')
        
        # Create test JSON file
        test_data = [
            {'content': 'Test content 1', 'alias': 'test1'},
            {'content': 'Test content 2', 'alias': 'test2'}
        ]
        
        json_file = os.path.join(self.temp_dir, 'test.json')
        with open(json_file, 'w') as f:
            json.dump(test_data, f)
        
        preview = self.parser.preview(json_file, self.config)
        
        self.assertEqual(preview['record_count'], 2)
        self.assertEqual(len(preview['sample_records']), 2)
        self.assertIn('content', preview['detected_fields'])
        self.assertIn('alias', preview['detected_fields'])


class TestCSVParser(unittest.TestCase):
    """Test cases for CSV parser."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestCSVParser.setUp called')
        
        self.temp_dir = tempfile.mkdtemp()
        self.parser = CSVParser()
        self.config = ImportConfig()
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestCSVParser.tearDown called')
        
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_csv_parser_initialization(self):
        """Test CSV parser initialization."""
        print('[DEBUG] test_csv_parser_initialization called')
        
        self.assertIsNotNone(self.parser)
        self.assertIn('excel', self.parser.supported_dialects)
        self.assertIn('.csv', self.parser.get_supported_extensions())
    
    def test_csv_preview(self):
        """Test CSV file preview functionality."""
        print('[DEBUG] test_csv_preview called')
        
        # Create test CSV file
        csv_file = os.path.join(self.temp_dir, 'test.csv')
        with open(csv_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['content', 'alias', 'timestamp'])
            writer.writerow(['Test content 1', 'test1', '2024-01-01'])
            writer.writerow(['Test content 2', 'test2', '2024-01-02'])
        
        preview = self.parser.preview(csv_file, self.config)
        
        self.assertEqual(preview['record_count'], 2)
        self.assertEqual(len(preview['sample_records']), 2)
        self.assertIn('content', preview['detected_fields'])
        self.assertIn('alias', preview['detected_fields'])
        self.assertEqual(preview['fieldnames'], ['content', 'alias', 'timestamp'])


class TestDuplicateHandler(unittest.TestCase):
    """Test cases for duplicate handler."""
    
    def setUp(self):
        """Set up test environment."""
        print('[DEBUG] TestDuplicateHandler.setUp called')
        
        self.handler = DuplicateHandler()
    
    def tearDown(self):
        """Clean up test environment."""
        print('[DEBUG] TestDuplicateHandler.tearDown called')
    
    def test_duplicate_handler_initialization(self):
        """Test duplicate handler initialization."""
        print('[DEBUG] test_duplicate_handler_initialization called')
        
        self.assertIsNotNone(self.handler)
        self.assertIn('skip', self.handler.duplicate_strategies)
        self.assertIn('replace', self.handler.duplicate_strategies)
        self.assertIn('merge', self.handler.duplicate_strategies)
    
    def test_content_duplicate_detection(self):
        """Test content-based duplicate detection."""
        print('[DEBUG] test_content_duplicate_detection called')
        
        test_records = [
            {'content': 'Test content 1', 'alias': 'test1'},
            {'content': 'Test content 2', 'alias': 'test2'},
            {'content': 'Test content 1', 'alias': 'test3'},  # Duplicate content
        ]
        
        duplicate_info = self.handler.detect_duplicates(test_records, 'content')
        
        self.assertEqual(len(duplicate_info['internal_duplicates']), 1)
        self.assertEqual(len(duplicate_info['unique_records']), 2)
        self.assertEqual(duplicate_info['total_duplicates'], 1)
    
    def test_alias_duplicate_detection(self):
        """Test alias-based duplicate detection."""
        print('[DEBUG] test_alias_duplicate_detection called')
        
        test_records = [
            {'content': 'Test content 1', 'alias': 'test1'},
            {'content': 'Test content 2', 'alias': 'test2'},
            {'content': 'Test content 3', 'alias': 'test1'},  # Duplicate alias
        ]
        
        duplicate_info = self.handler.detect_duplicates(test_records, 'alias')
        
        self.assertEqual(len(duplicate_info['internal_duplicates']), 1)
        self.assertEqual(len(duplicate_info['unique_records']), 2)


def run_import_tests():
    """Run all import system tests."""
    print("🧪 Running Import System Tests")
    print("=" * 50)

    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestImportSystem))
    suite.addTests(loader.loadTestsFromTestCase(TestJSONParser))
    suite.addTests(loader.loadTestsFromTestCase(TestCSVParser))
    suite.addTests(loader.loadTestsFromTestCase(TestDuplicateHandler))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print("\n" + "=" * 50)
    print("📊 IMPORT SYSTEM TEST SUMMARY")
    print("=" * 50)

    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    passed = total_tests - failures - errors

    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed}")
    print(f"Failed: {failures}")
    print(f"Errors: {errors}")

    if failures == 0 and errors == 0:
        print("🎉 ALL IMPORT TESTS PASSED!")
        return True
    else:
        print("❌ SOME IMPORT TESTS FAILED!")
        return False


if __name__ == "__main__":
    run_import_tests()
