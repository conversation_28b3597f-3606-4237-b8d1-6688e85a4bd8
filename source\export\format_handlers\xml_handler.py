#!/usr/bin/env python3
"""
XML Export Handler for ClipsMore Application
Handles exporting clipboard data to XML format with schema validation.
"""

import os
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Any
from pathlib import Path


class XMLHandler:
    """
    XML format handler for export operations.
    Creates structured XML files with schema validation and namespace support.
    """
    
    def __init__(self):
        """Initialize the XML handler."""
        print('[DEBUG] XMLHandler.__init__ called')
        self.format_type = 'xml'
        self.file_extension = '.xml'
        self.namespace = 'http://clipsmore.app/export/v1'
        self.schema_version = '1.0'
    
    def export(self, data: List[Dict[str, Any]], output_path: str, 
               export_config: Dict[str, Any] = None) -> bool:
        """
        Export data to XML format.
        
        Args:
            data: List of dictionaries containing clip data
            output_path: Output file path
            export_config: Export configuration options
                - include_schema: Include XML schema reference (default: True)
                - use_namespace: Use XML namespace (default: True)
                - pretty_print: Format XML with indentation (default: True)
                - include_metadata: Include metadata section (default: True)
                - validate_xml: Validate XML structure (default: True)
                - encoding: XML encoding (default: 'utf-8')
        
        Returns:
            True if export successful, False otherwise
        """
        print(f'[DEBUG] XMLHandler.export called for {len(data)} records')
        
        if export_config is None:
            export_config = {}
        
        try:
            # Create XML document
            root = self._create_xml_document(data, export_config)
            
            # Create ElementTree
            tree = ET.ElementTree(root)
            
            # Configure output
            encoding = export_config.get('encoding', 'utf-8')
            pretty_print = export_config.get('pretty_print', True)
            
            if pretty_print:
                self._indent_xml(root)
            
            # Write to file
            tree.write(output_path, encoding=encoding, xml_declaration=True)
            
            # Validate if requested
            if export_config.get('validate_xml', True):
                self._validate_xml_structure(output_path)
            
            print(f'[DEBUG] XML export completed: {output_path}')
            return True
            
        except Exception as e:
            print(f'[ERROR] XML export failed: {e}')
            return False
    
    def _create_xml_document(self, data: List[Dict[str, Any]], 
                           config: Dict[str, Any]) -> ET.Element:
        """Create XML document structure."""
        print('[DEBUG] XMLHandler._create_xml_document called')
        
        # Configuration
        use_namespace = config.get('use_namespace', True)
        include_metadata = config.get('include_metadata', True)
        include_schema = config.get('include_schema', True)
        
        # Create root element
        if use_namespace:
            root = ET.Element('clipsmore-export', 
                            attrib={'xmlns': self.namespace,
                                   'version': self.schema_version})
        else:
            root = ET.Element('clipsmore-export', 
                            attrib={'version': self.schema_version})
        
        # Add schema reference if requested
        if include_schema:
            root.set('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance')
            root.set('xsi:schemaLocation', 
                    f'{self.namespace} clipsmore-export-v1.xsd')
        
        # Add metadata
        if include_metadata:
            metadata_elem = self._create_metadata_element(data, config)
            root.append(metadata_elem)
        
        # Add clips data
        clips_elem = self._create_clips_element(data)
        root.append(clips_elem)
        
        return root
    
    def _create_metadata_element(self, data: List[Dict[str, Any]], 
                               config: Dict[str, Any]) -> ET.Element:
        """Create metadata element."""
        print('[DEBUG] XMLHandler._create_metadata_element called')
        
        metadata = ET.Element('metadata')
        
        # Export information
        export_info = ET.SubElement(metadata, 'export-info')
        
        ET.SubElement(export_info, 'application').text = 'ClipsMore'
        ET.SubElement(export_info, 'version').text = '1.0'
        ET.SubElement(export_info, 'export-date').text = datetime.now().isoformat()
        ET.SubElement(export_info, 'format').text = 'XML'
        ET.SubElement(export_info, 'schema-version').text = self.schema_version
        
        # Statistics
        stats = ET.SubElement(metadata, 'statistics')
        
        total_clips = len(data)
        business_cases = set()
        components = set()
        
        for record in data:
            if record.get('bus_case'):
                business_cases.add(record['bus_case'])
            if record.get('bus_component'):
                components.add(record['bus_component'])
        
        ET.SubElement(stats, 'total-clips').text = str(total_clips)
        ET.SubElement(stats, 'business-cases').text = str(len(business_cases))
        ET.SubElement(stats, 'components').text = str(len(components))
        
        # Business cases list
        if business_cases:
            bc_list = ET.SubElement(metadata, 'business-cases')
            for bc in sorted(business_cases):
                bc_elem = ET.SubElement(bc_list, 'business-case')
                bc_elem.text = bc
        
        # Components list
        if components:
            comp_list = ET.SubElement(metadata, 'components')
            for comp in sorted(components):
                comp_elem = ET.SubElement(comp_list, 'component')
                comp_elem.text = comp
        
        return metadata
    
    def _create_clips_element(self, data: List[Dict[str, Any]]) -> ET.Element:
        """Create clips element with all clip data."""
        print('[DEBUG] XMLHandler._create_clips_element called')
        
        clips = ET.Element('clips')
        clips.set('count', str(len(data)))
        
        for record in data:
            clip_elem = self._create_clip_element(record)
            clips.append(clip_elem)
        
        return clips
    
    def _create_clip_element(self, record: Dict[str, Any]) -> ET.Element:
        """Create XML element for a single clip."""
        clip = ET.Element('clip')
        
        # Set attributes
        if 'clip_id' in record:
            clip.set('id', str(record['clip_id']))
        
        if 'transaction_id' in record:
            clip.set('transaction-id', str(record['transaction_id']))
        
        # Basic fields
        if 'alias' in record and record['alias']:
            ET.SubElement(clip, 'alias').text = str(record['alias'])
        
        if 'content' in record and record['content']:
            content_elem = ET.SubElement(clip, 'content')
            content_elem.text = str(record['content'])
            # Add content length as attribute
            content_elem.set('length', str(len(record['content'])))
        
        if 'timestamp' in record and record['timestamp']:
            ET.SubElement(clip, 'timestamp').text = str(record['timestamp'])
        
        # Business case and component
        if record.get('bus_case') or record.get('bus_component'):
            business_elem = ET.SubElement(clip, 'business-context')
            
            if record.get('bus_case'):
                ET.SubElement(business_elem, 'case').text = str(record['bus_case'])
                if 'more_bus_id' in record:
                    business_elem.find('case').set('id', str(record['more_bus_id']))
            
            if record.get('bus_component'):
                ET.SubElement(business_elem, 'component').text = str(record['bus_component'])
                if 'more_comp_id' in record:
                    business_elem.find('component').set('id', str(record['more_comp_id']))
        
        # Additional metadata
        if any(key in record for key in ['created_date', 'modified_date', 'tree_position']):
            meta_elem = ET.SubElement(clip, 'metadata')
            
            if 'created_date' in record and record['created_date']:
                ET.SubElement(meta_elem, 'created').text = str(record['created_date'])
            
            if 'modified_date' in record and record['modified_date']:
                ET.SubElement(meta_elem, 'modified').text = str(record['modified_date'])
            
            if 'tree_position' in record:
                ET.SubElement(meta_elem, 'position').text = str(record['tree_position'])
        
        return clip
    
    def _indent_xml(self, elem: ET.Element, level: int = 0):
        """Add indentation to XML for pretty printing."""
        indent = "\n" + level * "  "
        
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = indent + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = indent
            
            for child in elem:
                self._indent_xml(child, level + 1)
            
            if not child.tail or not child.tail.strip():
                child.tail = indent
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = indent
    
    def _validate_xml_structure(self, file_path: str) -> bool:
        """Validate XML structure and well-formedness."""
        print(f'[DEBUG] XMLHandler._validate_xml_structure called for {file_path}')

        try:
            # Parse the file to check well-formedness
            tree = ET.parse(file_path)
            root = tree.getroot()

            # Handle namespaced root element
            root_tag = root.tag
            if '}' in root_tag:
                # Remove namespace prefix for validation
                root_tag = root_tag.split('}')[1]

            # Basic structure validation
            if root_tag != 'clipsmore-export':
                raise ValueError(f"Invalid root element: {root_tag}")

            # Check for required elements using simple iteration
            clips_elem = None
            for child in root:
                child_tag = child.tag
                if '}' in child_tag:
                    child_tag = child_tag.split('}')[1]
                if child_tag == 'clips':
                    clips_elem = child
                    break

            if clips_elem is None:
                raise ValueError("Missing clips element")

            # Validate clip elements
            for clip in clips_elem:
                clip_tag = clip.tag
                if '}' in clip_tag:
                    clip_tag = clip_tag.split('}')[1]
                if clip_tag == 'clip':
                    # Check for content element
                    has_content = False
                    for clip_child in clip:
                        content_tag = clip_child.tag
                        if '}' in content_tag:
                            content_tag = content_tag.split('}')[1]
                        if content_tag == 'content':
                            has_content = True
                            break
                    if not has_content:
                        print('[WARNING] Clip without content found')

            print('[DEBUG] XML validation successful')
            return True

        except ET.ParseError as e:
            print(f'[ERROR] XML parse error: {e}')
            return False
        except Exception as e:
            print(f'[ERROR] XML validation error: {e}')
            return False
    
    def create_schema_file(self, output_path: str) -> bool:
        """Create XSD schema file for the XML export format."""
        print(f'[DEBUG] XMLHandler.create_schema_file called for {output_path}')
        
        schema_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="{self.namespace}"
           xmlns:cm="{self.namespace}"
           elementFormDefault="qualified">

  <!-- Root element -->
  <xs:element name="clipsmore-export">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="metadata" type="cm:MetadataType" minOccurs="0"/>
        <xs:element name="clips" type="cm:ClipsType"/>
      </xs:sequence>
      <xs:attribute name="version" type="xs:string" use="required"/>
    </xs:complexType>
  </xs:element>

  <!-- Metadata type -->
  <xs:complexType name="MetadataType">
    <xs:sequence>
      <xs:element name="export-info" type="cm:ExportInfoType"/>
      <xs:element name="statistics" type="cm:StatisticsType"/>
      <xs:element name="business-cases" type="cm:BusinessCasesType" minOccurs="0"/>
      <xs:element name="components" type="cm:ComponentsType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Export info type -->
  <xs:complexType name="ExportInfoType">
    <xs:sequence>
      <xs:element name="application" type="xs:string"/>
      <xs:element name="version" type="xs:string"/>
      <xs:element name="export-date" type="xs:dateTime"/>
      <xs:element name="format" type="xs:string"/>
      <xs:element name="schema-version" type="xs:string"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Statistics type -->
  <xs:complexType name="StatisticsType">
    <xs:sequence>
      <xs:element name="total-clips" type="xs:nonNegativeInteger"/>
      <xs:element name="business-cases" type="xs:nonNegativeInteger"/>
      <xs:element name="components" type="xs:nonNegativeInteger"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Business cases type -->
  <xs:complexType name="BusinessCasesType">
    <xs:sequence>
      <xs:element name="business-case" type="xs:string" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Components type -->
  <xs:complexType name="ComponentsType">
    <xs:sequence>
      <xs:element name="component" type="xs:string" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Clips type -->
  <xs:complexType name="ClipsType">
    <xs:sequence>
      <xs:element name="clip" type="cm:ClipType" maxOccurs="unbounded"/>
    </xs:sequence>
    <xs:attribute name="count" type="xs:nonNegativeInteger" use="required"/>
  </xs:complexType>

  <!-- Clip type -->
  <xs:complexType name="ClipType">
    <xs:sequence>
      <xs:element name="alias" type="xs:string" minOccurs="0"/>
      <xs:element name="content" type="cm:ContentType"/>
      <xs:element name="timestamp" type="xs:dateTime" minOccurs="0"/>
      <xs:element name="business-context" type="cm:BusinessContextType" minOccurs="0"/>
      <xs:element name="metadata" type="cm:ClipMetadataType" minOccurs="0"/>
    </xs:sequence>
    <xs:attribute name="id" type="xs:positiveInteger"/>
    <xs:attribute name="transaction-id" type="xs:positiveInteger"/>
  </xs:complexType>

  <!-- Content type -->
  <xs:complexType name="ContentType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="length" type="xs:nonNegativeInteger"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <!-- Business context type -->
  <xs:complexType name="BusinessContextType">
    <xs:sequence>
      <xs:element name="case" type="cm:BusinessCaseType" minOccurs="0"/>
      <xs:element name="component" type="cm:ComponentType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Business case type -->
  <xs:complexType name="BusinessCaseType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="id" type="xs:positiveInteger"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <!-- Component type -->
  <xs:complexType name="ComponentType">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="id" type="xs:positiveInteger"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <!-- Clip metadata type -->
  <xs:complexType name="ClipMetadataType">
    <xs:sequence>
      <xs:element name="created" type="xs:dateTime" minOccurs="0"/>
      <xs:element name="modified" type="xs:dateTime" minOccurs="0"/>
      <xs:element name="position" type="xs:integer" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

</xs:schema>'''
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(schema_content)
            
            print(f'[DEBUG] XSD schema file created: {output_path}')
            return True
            
        except Exception as e:
            print(f'[ERROR] Failed to create schema file: {e}')
            return False
    
    def get_format_name(self) -> str:
        """Get human-readable format name."""
        return "XML"
    
    def get_file_extension(self) -> str:
        """Get file extension for this format."""
        return self.file_extension
    
    def get_format_description(self) -> str:
        """Get format description."""
        return "XML files with schema validation and namespace support"
