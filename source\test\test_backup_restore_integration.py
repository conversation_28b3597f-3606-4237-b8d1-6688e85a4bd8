#!/usr/bin/env python3
"""
Integration test for backup and restore functionality.
Tests the complete backup/restore workflow.
"""

import os
import sys
import tempfile
import shutil
import sqlite3
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backup.backup_manager import BackupManager
from backup.restore_manager import RestoreManager
from DB.db_connection import ConnectionPoolManager


def test_backup_restore_integration():
    """Test complete backup and restore workflow."""
    print('[TEST] Starting backup/restore integration test')
    
    # Create temporary directory for test
    test_dir = tempfile.mkdtemp(prefix='clipmore_test_')
    print(f'[TEST] Using test directory: {test_dir}')
    
    try:
        # Initialize managers
        backup_manager = BackupManager()
        restore_manager = RestoreManager()
        
        # Create a test backup
        backup_filename = f"test_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        backup_path = os.path.join(test_dir, backup_filename)
        
        print('[TEST] Creating backup...')
        backup_success = backup_manager.create_backup(backup_path, {
            'compression': False,  # Disable compression for easier testing
            'verification': True
        })
        
        if not backup_success:
            print('[ERROR] Backup creation failed')
            return False
        
        print(f'[TEST] Backup created successfully: {backup_path}')
        
        # Verify backup file exists and has content
        if not os.path.exists(backup_path):
            print('[ERROR] Backup file does not exist')
            return False
        
        if os.path.getsize(backup_path) == 0:
            print('[ERROR] Backup file is empty')
            return False
        
        print('[TEST] Backup file validation passed')
        
        # Test restore validation (without actually restoring)
        print('[TEST] Testing restore validation...')
        
        # Test backup file validation
        if not restore_manager._validate_backup_file(backup_path):
            print('[ERROR] Backup file validation failed')
            return False
        
        print('[TEST] Backup file validation passed')
        
        # Test backup integrity verification
        if not restore_manager._verify_backup_integrity(backup_path):
            print('[ERROR] Backup integrity verification failed')
            return False
        
        print('[TEST] Backup integrity verification passed')
        
        # Test backup database structure verification
        if not restore_manager._verify_backup_database_structure(backup_path):
            print('[ERROR] Backup database structure verification failed')
            return False
        
        print('[TEST] Backup database structure verification passed')
        
        print('[TEST] All backup/restore integration tests passed!')
        return True
        
    except Exception as e:
        print(f'[ERROR] Test failed with exception: {e}')
        return False
        
    finally:
        # Clean up test directory
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f'[TEST] Cleaned up test directory: {test_dir}')


def test_restore_manager_methods():
    """Test individual RestoreManager methods."""
    print('[TEST] Testing RestoreManager methods')
    
    restore_manager = RestoreManager()
    
    # Test progress callback
    progress_updates = []
    def progress_callback(percentage, message):
        progress_updates.append((percentage, message))
    
    restore_manager.set_progress_callback(progress_callback)
    restore_manager._update_progress(50, "Test progress")
    
    if not progress_updates or progress_updates[0] != (50, "Test progress"):
        print('[ERROR] Progress callback test failed')
        return False
    
    print('[TEST] Progress callback test passed')
    
    # Test cancellation
    restore_manager.cancel_restore()
    if not restore_manager.cancel_requested:
        print('[ERROR] Cancellation test failed')
        return False
    
    print('[TEST] Cancellation test passed')
    
    # Test invalid backup file validation
    if restore_manager._validate_backup_file('nonexistent_file.db'):
        print('[ERROR] Invalid file validation should have failed')
        return False
    
    print('[TEST] Invalid file validation test passed')
    
    print('[TEST] All RestoreManager method tests passed!')
    return True


if __name__ == '__main__':
    print('=' * 60)
    print('BACKUP/RESTORE INTEGRATION TEST')
    print('=' * 60)
    
    # Run tests
    test1_passed = test_backup_restore_integration()
    test2_passed = test_restore_manager_methods()
    
    print('=' * 60)
    print('TEST RESULTS:')
    print(f'  Backup/Restore Integration: {"PASSED" if test1_passed else "FAILED"}')
    print(f'  RestoreManager Methods: {"PASSED" if test2_passed else "FAILED"}')
    
    if test1_passed and test2_passed:
        print('  OVERALL: ALL TESTS PASSED ✅')
        sys.exit(0)
    else:
        print('  OVERALL: SOME TESTS FAILED ❌')
        sys.exit(1)
